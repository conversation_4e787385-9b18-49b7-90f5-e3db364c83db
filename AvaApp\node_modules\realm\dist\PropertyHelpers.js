"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2022 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPropertyHelpers = void 0;
const TypeHelpers_1 = require("./TypeHelpers");
const Array_1 = require("./property-accessors/Array");
const Object_1 = require("./property-accessors/Object");
const Dictionary_1 = require("./property-accessors/Dictionary");
const Set_1 = require("./property-accessors/Set");
const Int_1 = require("./property-accessors/Int");
const Mixed_1 = require("./property-accessors/Mixed");
const default_1 = require("./property-accessors/default");
const ACCESSOR_FACTORIES = {
    [0 /* binding.PropertyType.Int */]: Int_1.createIntPropertyAccessor,
    [7 /* binding.PropertyType.Object */]: Object_1.createObjectPropertyAccessor,
    [128 /* binding.PropertyType.Array */]: Array_1.createArrayPropertyAccessor,
    [512 /* binding.PropertyType.Dictionary */]: Dictionary_1.createDictionaryPropertyAccessor,
    [256 /* binding.PropertyType.Set */]: Set_1.createSetPropertyAccessor,
    [9 /* binding.PropertyType.Mixed */]: Mixed_1.createMixedPropertyAccessor,
    [8 /* binding.PropertyType.LinkingObjects */]() {
        return {
            get() {
                throw new Error("Getting linking objects happens through Array");
            },
            set() {
                throw new Error("Setting linking objects happens through Array");
            },
        };
    },
};
function createDefaultPropertyHelpers(options) {
    const { typeHelpers, columnKey, embedded, objectType } = options;
    return {
        ...(0, default_1.createDefaultPropertyAccessor)(options),
        ...typeHelpers,
        type: options.type,
        columnKey,
        embedded,
        objectType,
    };
}
function getPropertyHelpers(type, options) {
    const { typeHelpers, columnKey, embedded, objectType } = options;
    const accessorFactory = ACCESSOR_FACTORIES[type];
    if (accessorFactory) {
        return { ...accessorFactory(options), ...typeHelpers, type: options.type, columnKey, embedded, objectType };
    }
    else {
        return createDefaultPropertyHelpers(options);
    }
}
/** @internal */
function createPropertyHelpers(property, options) {
    const collectionType = property.type & 896 /* binding.PropertyType.Collection */;
    const typeOptions = {
        realm: options.realm,
        name: property.publicName || property.name,
        getClassHelpers: options.getClassHelpers,
        objectType: property.objectType,
        objectSchemaName: property.objectSchemaName,
        optional: !!(property.type & 64 /* binding.PropertyType.Nullable */),
        presentation: property.presentation,
    };
    if (collectionType) {
        return getPropertyHelpers(collectionType, {
            ...property,
            ...options,
            ...typeOptions,
            typeHelpers: (0, TypeHelpers_1.getTypeHelpers)(collectionType, typeOptions),
        });
    }
    else {
        const itemType = (0, TypeHelpers_1.toItemType)(property.type);
        return getPropertyHelpers(itemType, {
            ...property,
            ...options,
            ...typeOptions,
            typeHelpers: (0, TypeHelpers_1.getTypeHelpers)(itemType, typeOptions),
        });
    }
}
exports.createPropertyHelpers = createPropertyHelpers;
//# sourceMappingURL=PropertyHelpers.js.map