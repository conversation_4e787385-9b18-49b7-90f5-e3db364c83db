import type { binding } from "../binding";
import type { ClassHelpers } from "../ClassHelpers";
import type { PresentationPropertyTypeName } from "../schema";
import type { ObjCreator, UpdateMode } from "../Object";
import type { Realm } from "../Realm";
/** @internal */
export type TypeHelpers<T = unknown> = {
    toBinding(value: T, options?: {
        createObj?: ObjCreator;
        updateMode?: UpdateMode;
        isQueryArg?: boolean;
    }): binding.MixedArg;
    fromBinding(value: unknown): T;
};
/** @internal */
export type TypeOptions = {
    realm: Realm;
    name: string;
    optional: boolean;
    objectType: string | undefined;
    objectSchemaName: string | undefined;
    presentation?: PresentationPropertyTypeName;
    getClassHelpers(nameOrTableKey: string | binding.TableKey): ClassHelpers;
};
