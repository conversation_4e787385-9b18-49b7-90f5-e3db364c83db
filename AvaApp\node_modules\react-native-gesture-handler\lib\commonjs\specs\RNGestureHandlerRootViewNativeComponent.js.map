{"version": 3, "sources": ["RNGestureHandlerRootViewNativeComponent.ts"], "names": [], "mappings": ";;;;;;;AAAA;;;;eAKe,qCAAoC,0BAApC,C", "sourcesContent": ["import codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';\nimport type { ViewProps } from 'react-native';\n\ninterface NativeProps extends ViewProps {}\n\nexport default codegenNativeComponent<NativeProps>('RNGestureHandlerRootView');\n"]}