{"version": 3, "names": ["_NativeSafeAreaContext", "_interopRequireDefault", "require", "e", "__esModule", "default", "initialWindowMetrics", "exports", "NativeSafeAreaContext", "getConstants", "initialWindowSafeAreaInsets", "insets"], "sourceRoot": "../../src", "sources": ["InitialWindow.native.ts"], "mappings": ";;;;;;AACA,IAAAA,sBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAkE,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE3D,MAAMG,oBAAoB,GAAAC,OAAA,CAAAD,oBAAA,GAAIE,8BAAqB,EAAEC,YAAY,GAAG,CAAC,EACxEH,oBAAoB,IAAI,IAAuB;;AAEnD;AACA;AACA;AACO,MAAMI,2BAA2B,GAAAH,OAAA,CAAAG,2BAAA,GAAGJ,oBAAoB,EAAEK,MAAM", "ignoreList": []}