"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2022 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTypeHelpers = exports.toItemType = void 0;
const assert_1 = require("./assert");
const Int_1 = require("./type-helpers/Int");
const Bool_1 = require("./type-helpers/Bool");
const String_1 = require("./type-helpers/String");
const Data_1 = require("./type-helpers/Data");
const Date_1 = require("./type-helpers/Date");
const Mixed_1 = require("./type-helpers/Mixed");
const ObjectId_1 = require("./type-helpers/ObjectId");
const Float_1 = require("./type-helpers/Float");
const Double_1 = require("./type-helpers/Double");
const Object_1 = require("./type-helpers/Object");
const LinkingObjects_1 = require("./type-helpers/LinkingObjects");
const Decimal_1 = require("./type-helpers/Decimal");
const Uuid_1 = require("./type-helpers/Uuid");
const Array_1 = require("./type-helpers/Array");
function createUnsupportedTypeHelpers() {
    return {
        fromBinding() {
            throw new Error("Not yet supported");
        },
        toBinding() {
            throw new Error("Not yet supported");
        },
    };
}
const TYPES_MAPPING = {
    [0 /* binding.PropertyType.Int */]: Int_1.createIntTypeHelpers,
    [1 /* binding.PropertyType.Bool */]: Bool_1.createBoolTypeHelpers,
    [2 /* binding.PropertyType.String */]: String_1.createStringTypeHelpers,
    [3 /* binding.PropertyType.Data */]: Data_1.createDataTypeHelpers,
    [4 /* binding.PropertyType.Date */]: Date_1.createDateTypeHelpers,
    [5 /* binding.PropertyType.Float */]: Float_1.createFloatTypeHelpers,
    [6 /* binding.PropertyType.Double */]: Double_1.createDoubleTypeHelpers,
    [7 /* binding.PropertyType.Object */]: Object_1.createObjectTypeHelpers,
    [8 /* binding.PropertyType.LinkingObjects */]: LinkingObjects_1.createLinkingObjectsTypeHelpers,
    [9 /* binding.PropertyType.Mixed */]: Mixed_1.createMixedTypeHelpers,
    [10 /* binding.PropertyType.ObjectId */]: ObjectId_1.createObjectIdTypeHelpers,
    [11 /* binding.PropertyType.Decimal */]: Decimal_1.createDecimalTypeHelpers,
    [12 /* binding.PropertyType.Uuid */]: Uuid_1.createUuidTypeHelpers,
    [128 /* binding.PropertyType.Array */]: Array_1.createArrayTypeHelpers,
    // Unsupported below
    [256 /* binding.PropertyType.Set */]: createUnsupportedTypeHelpers,
    [512 /* binding.PropertyType.Dictionary */]: createUnsupportedTypeHelpers,
};
/** @internal */
function toItemType(type) {
    return type & ~960 /* binding.PropertyType.Flags */;
}
exports.toItemType = toItemType;
/** @internal */
function getTypeHelpers(type, options) {
    const helpers = TYPES_MAPPING[type];
    (0, assert_1.assert)(helpers, `Unexpected type ${type}`);
    return helpers(options);
}
exports.getTypeHelpers = getTypeHelpers;
//# sourceMappingURL=TypeHelpers.js.map