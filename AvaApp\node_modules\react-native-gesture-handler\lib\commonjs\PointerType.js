"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PointerType = void 0;
let PointerType;
exports.PointerType = PointerType;

(function (PointerType) {
  PointerType[PointerType["TOUCH"] = 0] = "TOUCH";
  PointerType[PointerType["STYLUS"] = 1] = "STYLUS";
  PointerType[PointerType["MOUSE"] = 2] = "MOUSE";
  PointerType[PointerType["KEY"] = 3] = "KEY";
  PointerType[PointerType["OTHER"] = 4] = "OTHER";
})(PointerType || (exports.PointerType = PointerType = {}));
//# sourceMappingURL=PointerType.js.map