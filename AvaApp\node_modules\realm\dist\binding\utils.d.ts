/**
 * Throws an error when a property is accessed before the native module has been injected.
 * @internal
 */
export declare function _throwOnAccess(propertyName: string): void;
export declare class Float {
    value: number;
    constructor(value: number);
    valueOf(): number;
}
export declare class Status {
    isOk: boolean;
    code?: number;
    reason?: string;
    constructor(isOk: boolean);
}
export declare const ListSentinel: unique symbol;
export declare const DictionarySentinel: unique symbol;
