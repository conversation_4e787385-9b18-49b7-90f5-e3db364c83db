/**
 * A logical counter representation for performing numeric updates that need
 * to be synchronized as sequentially consistent events rather than individual
 * reassignments of the number.
 *
 * For instance, offline Client 1 and Client 2 which both see `Counter.value`
 * as `0`, can both call `Counter.increment(1)`. Once online, the value will
 * converge to `2`.
 *
 * ### Counter types are *not* supported as:
 *
 * - `Mixed` values
 * - Primary keys
 * - Inside collections
 * - Query arguments for placeholders (e.g. `$0`) in {@link Realm.Results.filtered | filtered()}
 *   - If you need to use the value of the `Counter` when filtering, use `Counter.value`.
 *
 * ### Declaring a counter
 *
 * A property schema is declared as either:
 * - `"counter"`
 * - `{ type: "int", presentation: "counter" }`
 *
 * ### Creating a counter
 *
 * Use a `number` when creating your counter on a {@link Realm.Object}.
 *
 * ```typescript
 * realm.write(() => {
 *   realm.create(MyObject, { _id: "123", counter: 0 });
 * });
 * ```
 *
 * ### Updating the count
 *
 * Use the instance methods to update the underlying count.
 *
 * ### Nullability
 *
 * The above property schema can be extended to allow a nullable counter.
 * A `Counter` never stores `null` values itself, but the counter property
 * on the {@link Realm.Object} (e.g. `myRealmObject.myCounter`) can be `null`.
 *
 * To create a counter from a previously `null` value, or to reset a nullable
 * counter to `null`, use {@link UpdateMode.Modified} or {@link UpdateMode.All}.
 *
 * ```typescript
 * realm.write(() => {
 *   realm.create(MyObject, { _id: "123", counter: 0 }, UpdateMode.Modified);
 * });
 * ```
 */
export declare class Counter {
    /**
     * The current count.
     */
    get value(): number;
    /**
     * Increment the count.
     * @param by The value to increment by. (Default: `1`)
     */
    increment(by?: number): void;
    /**
     * Decrement the count.
     * @param by The value to decrement by. (Default: `1`)
     */
    decrement(by?: number): void;
    /**
     * Reset the count.
     * @param value The value to reset the count to.
     * @warning
     * Unlike {@link Counter.increment | increment} and {@link Counter.decrement | decrement},
     * setting the count behaves like regular individual updates to the underlying value.
     */
    set(value: number): void;
}
//# sourceMappingURL=Counter.d.ts.map