{"version": 3, "sources": ["createNativeWrapper.tsx"], "names": ["NATIVE_WRAPPER_PROPS_FILTER", "nativeViewProps", "createNativeWrapper", "Component", "config", "ComponentWrapper", "React", "forwardRef", "props", "ref", "gestureHandlerProps", "childProps", "Object", "keys", "reduce", "res", "key", "<PERSON><PERSON><PERSON><PERSON>", "includes", "enabled", "hitSlop", "testID", "_ref", "_gestureHandlerRef", "node", "current", "handlerTag", "displayName", "render", "name"], "mappings": ";;;;;;;AAAA;;AAGA;;;;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,2BAA2B,GAAG,CAClC,GAAGC,yCAD+B,EAElC,uBAFkC,EAGlC,6BAHkC,CAApC;;AAMe,SAASC,mBAAT,CACbC,SADa,EAEbC,MAA+C,GAAG,EAFrC,EAGb;AAAA;;AACA,QAAMC,gBAAgB,gBAAGC,KAAK,CAACC,UAAN,CAGvB,CAACC,KAAD,EAAQC,GAAR,KAAgB;AAChB;AACA,UAAM;AAAEC,MAAAA,mBAAF;AAAuBC,MAAAA;AAAvB,QAAsCC,MAAM,CAACC,IAAP,CAAYL,KAAZ,EAAmBM,MAAnB,CAC1C,CAACC,GAAD,EAAMC,GAAN,KAAc;AACZ;AACA,YAAMC,WAA8B,GAAGjB,2BAAvC;;AACA,UAAIiB,WAAW,CAACC,QAAZ,CAAqBF,GAArB,CAAJ,EAA+B;AAC7B;AACAD,QAAAA,GAAG,CAACL,mBAAJ,CAAwBM,GAAxB,IAA+BR,KAAK,CAACQ,GAAD,CAApC;AACD,OAHD,MAGO;AACL;AACAD,QAAAA,GAAG,CAACJ,UAAJ,CAAeK,GAAf,IAAsBR,KAAK,CAACQ,GAAD,CAA3B;AACD;;AACD,aAAOD,GAAP;AACD,KAZyC,EAa1C;AACEL,MAAAA,mBAAmB,EAAE,EAAE,GAAGN;AAAL,OADvB;AACsC;AACpCO,MAAAA,UAAU,EAAE;AACVQ,QAAAA,OAAO,EAAEX,KAAK,CAACW,OADL;AAEVC,QAAAA,OAAO,EAAEZ,KAAK,CAACY,OAFL;AAGVC,QAAAA,MAAM,EAAEb,KAAK,CAACa;AAHJ;AAFd,KAb0C,CAA5C;;AAsBA,UAAMC,IAAI,GAAG,kBAA+B,IAA/B,CAAb;;AACA,UAAMC,kBAAkB,GAAG,kBAA+B,IAA/B,CAA3B;;AACA,mCACEd,GADF,EAEE;AACA,UAAM;AACJ,YAAMe,IAAI,GAAGD,kBAAkB,CAACE,OAAhC,CADI,CAEJ;;AACA,UAAIH,IAAI,CAACG,OAAL,IAAgBD,IAApB,EAA0B;AACxB;AACAF,QAAAA,IAAI,CAACG,OAAL,CAAaC,UAAb,GAA0BF,IAAI,CAACE,UAA/B;AACA,eAAOJ,IAAI,CAACG,OAAZ;AACD;;AACD,aAAO,IAAP;AACD,KAZH,EAaE,CAACH,IAAD,EAAOC,kBAAP,CAbF;AAeA,wBACE,oBAAC,kDAAD,eACMb,mBADN;AAEE;AACA,MAAA,GAAG,EAAEa;AAHP,qBAIE,oBAAC,SAAD,eAAeZ,UAAf;AAA2B,MAAA,GAAG,EAAEW;AAAhC,OAJF,CADF;AAQD,GApDwB,CAAzB,CADA,CAuDA;;AACAjB,EAAAA,gBAAgB,CAACsB,WAAjB,GACE,CAAAxB,SAAS,SAAT,IAAAA,SAAS,WAAT,YAAAA,SAAS,CAAEwB,WAAX,OACA;AACAxB,EAAAA,SAFA,aAEAA,SAFA,4CAEAA,SAAS,CAAEyB,MAFX,sDAEA,kBAAmBC,IAFnB,KAGC,OAAO1B,SAAP,KAAqB,QAArB,IAAiCA,SAHlC,IAIA,kBALF;AAOA,SAAOE,gBAAP;AACD", "sourcesContent": ["import * as React from 'react';\nimport { useImperativeHandle, useRef } from 'react';\n\nimport {\n  NativeViewGestureHandler,\n  NativeViewGestureHandlerProps,\n  nativeViewProps,\n} from './NativeViewGestureHandler';\n\n/*\n * This array should consist of:\n *   - All keys in propTypes from NativeGestureHandler\n *     (and all keys in GestureHandlerPropTypes)\n *   - 'onGestureHandlerEvent'\n *   - 'onGestureHandlerStateChange'\n */\nconst NATIVE_WRAPPER_PROPS_FILTER = [\n  ...nativeViewProps,\n  'onGestureHandlerEvent',\n  'onGestureHandlerStateChange',\n] as const;\n\nexport default function createNativeWrapper<P>(\n  Component: React.ComponentType<P>,\n  config: Readonly<NativeViewGestureHandlerProps> = {}\n) {\n  const ComponentWrapper = React.forwardRef<\n    React.ComponentType<any>,\n    P & NativeViewGestureHandlerProps\n  >((props, ref) => {\n    // Filter out props that should be passed to gesture handler wrapper\n    const { gestureHandlerProps, childProps } = Object.keys(props).reduce(\n      (res, key) => {\n        // TS being overly protective with it's types, see https://github.com/microsoft/TypeScript/issues/26255#issuecomment-458013731 for more info\n        const allowedKeys: readonly string[] = NATIVE_WRAPPER_PROPS_FILTER;\n        if (allowedKeys.includes(key)) {\n          // @ts-ignore FIXME(TS)\n          res.gestureHandlerProps[key] = props[key];\n        } else {\n          // @ts-ignore FIXME(TS)\n          res.childProps[key] = props[key];\n        }\n        return res;\n      },\n      {\n        gestureHandlerProps: { ...config }, // Watch out not to modify config\n        childProps: {\n          enabled: props.enabled,\n          hitSlop: props.hitSlop,\n          testID: props.testID,\n        } as P,\n      }\n    );\n    const _ref = useRef<React.ComponentType<P>>(null);\n    const _gestureHandlerRef = useRef<React.ComponentType<P>>(null);\n    useImperativeHandle(\n      ref,\n      // @ts-ignore TODO(TS) decide how nulls work in this context\n      () => {\n        const node = _gestureHandlerRef.current;\n        // Add handlerTag for relations config\n        if (_ref.current && node) {\n          // @ts-ignore FIXME(TS) think about createHandler return type\n          _ref.current.handlerTag = node.handlerTag;\n          return _ref.current;\n        }\n        return null;\n      },\n      [_ref, _gestureHandlerRef]\n    );\n    return (\n      <NativeViewGestureHandler\n        {...gestureHandlerProps}\n        // @ts-ignore TODO(TS)\n        ref={_gestureHandlerRef}>\n        <Component {...childProps} ref={_ref} />\n      </NativeViewGestureHandler>\n    );\n  });\n\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n  ComponentWrapper.displayName =\n    Component?.displayName ||\n    // @ts-ignore if render doesn't exist it will return undefined and go further\n    Component?.render?.name ||\n    (typeof Component === 'string' && Component) ||\n    'ComponentWrapper';\n\n  return ComponentWrapper;\n}\n"]}