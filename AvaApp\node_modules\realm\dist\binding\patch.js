"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.applyPatch = void 0;
/**
 * Applies SDK level patches to the binding.
 * This should only be called after the binding has been injected.
 * @internal
 */
function applyPatch(binding) {
    binding.IndexSet.prototype.asIndexes = function* () {
        for (const [from, to] of this) {
            let i = from;
            while (i < to) {
                yield i;
                i++;
            }
        }
    };
    binding.Timestamp.fromDate = (d) => binding.Timestamp.make(binding.Int64.numToInt(Math.floor(d.valueOf() / 1000)), (d.valueOf() % 1000) * 1000000);
    binding.Timestamp.prototype.toDate = function () {
        return new Date(Number(this.seconds) * 1000 + this.nanoseconds / 1000000);
    };
    binding.InvalidObjKey = class InvalidObjKey extends TypeError {
        constructor(input) {
            super(`Cannot convert '${input}' to an ObjKey`);
        }
    };
    binding.stringToObjKey = (input) => {
        try {
            return binding.Int64.strToInt(input);
        }
        catch {
            throw new binding.InvalidObjKey(input);
        }
    };
    binding.isEmptyObjKey = (objKey) => {
        // This relies on the JS representation of an ObjKey being a bigint
        return binding.Int64.equals(objKey, -1);
    };
}
exports.applyPatch = applyPatch;
//# sourceMappingURL=patch.js.map