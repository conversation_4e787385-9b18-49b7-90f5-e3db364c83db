"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2022 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Types = void 0;
const bson_1 = require("./bson");
const CounterNS = __importStar(require("./Counter"));
const ListNS = __importStar(require("./List"));
const SetNS = __importStar(require("./Set"));
const DictionaryNS = __importStar(require("./Dictionary"));
const ResultsNS = __importStar(require("./Results"));
const GlobalDate = Date;
// eslint-disable-next-line @typescript-eslint/no-namespace
var Types;
(function (Types) {
    Types.Decimal128 = bson_1.BSON.Decimal128;
    Types.ObjectId = bson_1.BSON.ObjectId;
    Types.UUID = bson_1.BSON.UUID;
    Types.Counter = CounterNS.Counter;
    Types.Date = GlobalDate;
    Types.Data = ArrayBuffer;
    Types.List = ListNS.List;
    Types.Set = SetNS.RealmSet;
    Types.Dictionary = DictionaryNS.Dictionary;
    Types.LinkingObjects = ResultsNS.Results;
})(Types = exports.Types || (exports.Types = {}));
//# sourceMappingURL=Types.js.map