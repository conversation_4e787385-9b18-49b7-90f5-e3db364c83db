"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2022 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
require("./expo-go-detection");
require("./binding");
require("./fs");
const Realm_1 = require("../../Realm");
const binding_1 = require("../binding");
// Clear the internal state to prevent crashes when reloading the app
binding_1.binding.RealmCoordinator.clearAllCaches();
require("../../deprecated-global");
module.exports = Realm_1.Realm;
//# sourceMappingURL=index.js.map