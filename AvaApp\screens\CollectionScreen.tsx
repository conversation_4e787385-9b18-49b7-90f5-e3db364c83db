import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  FlatList,
  TextInput,
  Alert,
  Modal,
  ScrollView,
  Image
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Colors, Gradients } from '../constants/Colors';
import { RootStackParamList } from '../types/navigation';
import { database, CollectionItem, CatalogItem } from '../database/database';

type CollectionScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Collection'>;

type ViewMode = 'collection' | 'search' | 'edit';

export const CollectionScreen: React.FC = () => {
  const navigation = useNavigation<CollectionScreenNavigationProp>();

  // State management
  const [viewMode, setViewMode] = useState<ViewMode>('collection');
  const [collectionItems, setCollectionItems] = useState<CollectionItem[]>([]);
  const [catalogItems, setCatalogItems] = useState<CatalogItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [searchLoading, setSearchLoading] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);

  // Edit modal state
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<CollectionItem | null>(null);
  const [editName, setEditName] = useState('');
  const [editDescription, setEditDescription] = useState('');
  const [editRarity, setEditRarity] = useState<'common' | 'rare' | 'epic' | 'legendary'>('common');

  useEffect(() => {
    initializeScreen();
  }, []);

  const initializeScreen = async () => {
    try {
      const user = await database.getUserByName('Ava');
      setCurrentUser(user);
      if (user) {
        await loadCollection(user.id);
      }

      // Debug: Check catalog data
      const totalItems = await database.getCatalogItemCount();
      console.log('Total catalog items available:', totalItems);

      if (totalItems > 0) {
        // Test search for common terms
        const testResults = await database.searchCatalog('charizard', 5);
        console.log('Test search for "charizard":', testResults.length, 'results');

        // Get a few sample items to see the data structure
        const sampleItems = await database.getAllCatalogItems(5);
        console.log('Sample catalog items:', sampleItems);
      }
    } catch (error) {
      console.error('Failed to initialize screen:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCollection = async (userId: number) => {
    try {
      const items = await database.getCollectionItems(userId);
      setCollectionItems(items);
    } catch (error) {
      console.error('Failed to load collection:', error);
    }
  };

  const searchCatalog = async (query: string) => {
    if (!query.trim()) {
      setCatalogItems([]);
      return;
    }

    setSearchLoading(true);
    try {
      console.log('Searching for:', query);

      // First check if we have any data at all
      const totalCount = await database.getCatalogItemCount();
      console.log('Total catalog items:', totalCount);

      if (totalCount === 0) {
        Alert.alert('No Data', 'The catalog appears to be empty. Please run the sync script first.');
        setCatalogItems([]);
        return;
      }

      const results = await database.searchCatalog(query, 50);
      console.log('Search results:', results.length, 'items found');
      console.log('First result:', results[0]);

      setCatalogItems(results);
    } catch (error) {
      console.error('Search failed:', error);
      Alert.alert('Search Error', 'Failed to search catalog: ' + error);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleSearchQueryChange = (text: string) => {
    setSearchQuery(text);
    if (text.length > 2) {
      searchCatalog(text);
    } else {
      setCatalogItems([]);
    }
  };

  const addToCollection = async (catalogItem: CatalogItem) => {
    if (!currentUser) return;

    try {
      // Check if item already exists in collection
      const exists = await database.isItemInCollection(currentUser.id, catalogItem.name);
      if (exists) {
        Alert.alert('Already in Collection', 'This item is already in your collection.');
        return;
      }

      await database.addCatalogItemToCollection(currentUser.id, catalogItem.id);
      await loadCollection(currentUser.id);
      Alert.alert('Success', 'Item added to your collection!');
    } catch (error) {
      Alert.alert('Error', 'Failed to add item to collection.');
    }
  };

  const editItem = (item: CollectionItem) => {
    setEditingItem(item);
    setEditName(item.name);
    setEditDescription(item.description || '');
    setEditRarity(item.rarity);
    setEditModalVisible(true);
  };

  const saveEdit = async () => {
    if (!editingItem || !currentUser) return;

    try {
      await database.updateCollectionItem(editingItem.id, {
        name: editName,
        description: editDescription,
        rarity: editRarity,
      });
      await loadCollection(currentUser.id);
      setEditModalVisible(false);
      Alert.alert('Success', 'Item updated successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to update item.');
    }
  };

  const deleteItem = async (item: CollectionItem) => {
    if (!currentUser) return;

    Alert.alert(
      'Delete Item',
      `Are you sure you want to remove "${item.name}" from your collection?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await database.deleteCollectionItem(item.id);
              await loadCollection(currentUser.id);
              Alert.alert('Success', 'Item removed from collection.');
            } catch (error) {
              Alert.alert('Error', 'Failed to remove item.');
            }
          },
        },
      ]
    );
  };

  const renderCollectionItem = ({ item }: { item: CollectionItem }) => (
    <View style={styles.itemCard}>
      <View style={styles.itemIcon}>
        <Ionicons
          name={item.rarity === 'legendary' ? 'star' : item.rarity === 'epic' ? 'diamond' : item.rarity === 'rare' ? 'trophy' : 'cube'}
          size={32}
          color={item.rarity === 'legendary' ? '#FFD700' : item.rarity === 'epic' ? '#9B59B6' : item.rarity === 'rare' ? '#3498DB' : '#95A5A6'}
        />
      </View>
      <Text style={styles.itemName}>{item.name}</Text>
      <Text style={styles.itemRarity}>{item.rarity.toUpperCase()}</Text>
      {item.description && <Text style={styles.itemDescription}>{item.description}</Text>}

      <View style={styles.itemActions}>
        <TouchableOpacity style={styles.actionButton} onPress={() => editItem(item)}>
          <Ionicons name="pencil" size={16} color={Colors.primary} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={() => deleteItem(item)}>
          <Ionicons name="trash" size={16} color="#E74C3C" />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderCatalogItem = ({ item }: { item: CatalogItem }) => (
    <View style={styles.catalogCard}>
      {/* Image */}
      <View style={styles.catalogImageContainer}>
        {item.image_url ? (
          <Image
            source={{ uri: item.image_url }}
            style={styles.catalogImage}
            onError={() => console.log('Failed to load image:', item.image_url)}
          />
        ) : (
          <View style={styles.catalogImagePlaceholder}>
            <Ionicons name="image-outline" size={32} color="#CCC" />
          </View>
        )}
      </View>

      {/* Info */}
      <View style={styles.catalogInfo}>
        <Text style={styles.catalogName} numberOfLines={2}>{item.name}</Text>
        {item.set_name && <Text style={styles.catalogSet} numberOfLines={1}>Set: {item.set_name}</Text>}
        <View style={styles.catalogMetaRow}>
          {item.category && <Text style={styles.catalogCategory}>{item.category}</Text>}
          {item.rarity && <Text style={styles.catalogRarity}>{item.rarity.toUpperCase()}</Text>}
        </View>
        {item.price && <Text style={styles.catalogPrice}>${item.price.toFixed(2)}</Text>}
      </View>

      {/* Add Button */}
      <TouchableOpacity style={styles.addButton} onPress={() => addToCollection(item)}>
        <Ionicons name="add" size={20} color={Colors.white} />
      </TouchableOpacity>
    </View>
  );

  const renderTabButton = (mode: ViewMode, icon: string, title: string) => (
    <TouchableOpacity
      style={[styles.tabButton, viewMode === mode && styles.activeTab]}
      onPress={() => setViewMode(mode)}
    >
      <Ionicons name={icon as any} size={20} color={viewMode === mode ? Colors.primary : Colors.white} />
      <Text style={[styles.tabText, viewMode === mode && styles.activeTabText]}>{title}</Text>
    </TouchableOpacity>
  );

  return (
    <LinearGradient colors={Gradients.background} style={styles.container}>
      <StatusBar style="light" />
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
            activeOpacity={0.7}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Collection</Text>
          <View style={styles.headerSpacer} />
        </View>

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          {renderTabButton('collection', 'library', 'My Items')}
          {renderTabButton('search', 'search', 'Add Items')}
        </View>

        {/* Search Bar (only in search mode) */}
        {viewMode === 'search' && (
          <View style={styles.searchContainer}>
            <View style={styles.searchBar}>
              <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search catalog..."
                placeholderTextColor="#666"
                value={searchQuery}
                onChangeText={handleSearchQueryChange}
                autoCapitalize="none"
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => {
                  setSearchQuery('');
                  setCatalogItems([]);
                }}>
                  <Ionicons name="close" size={20} color="#666" />
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}

        {/* Content */}
        <View style={styles.content}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Loading...</Text>
            </View>
          ) : viewMode === 'collection' ? (
            collectionItems.length > 0 ? (
              <FlatList
                data={collectionItems}
                renderItem={renderCollectionItem}
                keyExtractor={(item) => item.id.toString()}
                numColumns={2}
                contentContainerStyle={styles.listContainer}
                showsVerticalScrollIndicator={false}
              />
            ) : (
              <View style={styles.emptyContainer}>
                <Ionicons name="library-outline" size={64} color={Colors.white} />
                <Text style={styles.emptyTitle}>No Items Yet</Text>
                <Text style={styles.emptyText}>
                  Tap "Add Items" to search the catalog and build your collection!
                </Text>
              </View>
            )
          ) : (
            // Search mode
            searchLoading ? (
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>Searching...</Text>
              </View>
            ) : catalogItems.length > 0 ? (
              <FlatList
                data={catalogItems}
                renderItem={renderCatalogItem}
                keyExtractor={(item) => item.id.toString()}
                contentContainerStyle={styles.listContainer}
                showsVerticalScrollIndicator={false}
              />
            ) : searchQuery.length > 2 ? (
              <View style={styles.emptyContainer}>
                <Ionicons name="search" size={64} color={Colors.white} />
                <Text style={styles.emptyTitle}>No Results</Text>
                <Text style={styles.emptyText}>
                  Try a different search term
                </Text>
              </View>
            ) : (
              <View style={styles.emptyContainer}>
                <Ionicons name="search" size={64} color={Colors.white} />
                <Text style={styles.emptyTitle}>Search Catalog</Text>
                <Text style={styles.emptyText}>
                  Type at least 3 characters to search our catalog of 420K+ items
                </Text>
              </View>
            )
          )}
        </View>

        {/* Edit Modal */}
        <Modal
          visible={editModalVisible}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setEditModalVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>Edit Item</Text>

              <Text style={styles.inputLabel}>Name</Text>
              <TextInput
                style={styles.input}
                value={editName}
                onChangeText={setEditName}
                placeholder="Item name"
              />

              <Text style={styles.inputLabel}>Description</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={editDescription}
                onChangeText={setEditDescription}
                placeholder="Description (optional)"
                multiline
                numberOfLines={3}
              />

              <Text style={styles.inputLabel}>Rarity</Text>
              <View style={styles.rarityContainer}>
                {(['common', 'rare', 'epic', 'legendary'] as const).map((rarity) => (
                  <TouchableOpacity
                    key={rarity}
                    style={[styles.rarityButton, editRarity === rarity && styles.selectedRarity]}
                    onPress={() => setEditRarity(rarity)}
                  >
                    <Text style={[styles.rarityText, editRarity === rarity && styles.selectedRarityText]}>
                      {rarity.toUpperCase()}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <View style={styles.modalActions}>
                <TouchableOpacity style={styles.cancelButton} onPress={() => setEditModalVisible(false)}>
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.saveButton} onPress={saveEdit}>
                  <Text style={styles.saveButtonText}>Save</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    color: Colors.white,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginHorizontal: 4,
  },
  activeTab: {
    backgroundColor: Colors.white,
  },
  tabText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '600',
    color: Colors.white,
  },
  activeTabText: {
    color: Colors.primary,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderRadius: 25,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.black,
  },
  content: {
    flex: 1,
    paddingTop: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.white,
    fontWeight: '500',
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  itemCard: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    padding: 16,
    margin: 8,
    flex: 1,
    alignItems: 'center',
    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  itemIcon: {
    marginBottom: 12,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.black,
    textAlign: 'center',
    marginBottom: 4,
  },
  itemRarity: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666666',
    marginBottom: 8,
  },
  itemDescription: {
    fontSize: 12,
    color: '#888888',
    textAlign: 'center',
    lineHeight: 16,
    marginBottom: 8,
  },
  itemActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: 8,
  },
  actionButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#F8F9FA',
  },
  catalogCard: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 16,
    marginVertical: 4,
    flexDirection: 'row',
    alignItems: 'flex-start',
    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },
  catalogImageContainer: {
    marginRight: 12,
  },
  catalogImage: {
    width: 60,
    height: 84,
    borderRadius: 6,
    backgroundColor: '#F5F5F5',
  },
  catalogImagePlaceholder: {
    width: 60,
    height: 84,
    borderRadius: 6,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  catalogInfo: {
    flex: 1,
    paddingTop: 2,
  },
  catalogName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.black,
    marginBottom: 4,
    lineHeight: 20,
  },
  catalogSet: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 4,
  },
  catalogMetaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  catalogCategory: {
    fontSize: 11,
    color: '#888888',
    backgroundColor: '#F0F0F0',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  catalogRarity: {
    fontSize: 11,
    fontWeight: '500',
    color: Colors.primary,
    backgroundColor: 'rgba(39, 89, 240, 0.1)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  catalogPrice: {
    fontSize: 14,
    fontWeight: '600',
    color: '#27AE60',
  },
  addButton: {
    backgroundColor: Colors.primary,
    borderRadius: 20,
    padding: 8,
    marginLeft: 8,
    marginTop: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.white,
    marginTop: 20,
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: Colors.white,
    borderRadius: 20,
    padding: 24,
    margin: 20,
    width: '90%',
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.black,
    textAlign: 'center',
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.black,
    marginBottom: 8,
    marginTop: 12,
  },
  input: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: Colors.black,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  rarityContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  rarityButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    marginRight: 8,
    marginBottom: 8,
  },
  selectedRarity: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  rarityText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666666',
  },
  selectedRarityText: {
    color: Colors.white,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666666',
    textAlign: 'center',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: Colors.primary,
    marginLeft: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.white,
    textAlign: 'center',
  },
});
