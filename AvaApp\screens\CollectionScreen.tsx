import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, ScrollView, FlatList } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Colors, Gradients } from '../constants/Colors';
import { RootStackParamList } from '../types/navigation';
import { database, CollectionItem } from '../database/database';

type CollectionScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Collection'>;

export const CollectionScreen: React.FC = () => {
  const navigation = useNavigation<CollectionScreenNavigationProp>();
  const [items, setItems] = useState<CollectionItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCollection();
  }, []);

  const loadCollection = async () => {
    try {
      const user = await database.getUserByName('Ava');
      if (user) {
        const collectionItems = await database.getCollectionItems(user.id);
        setItems(collectionItems);
      }
    } catch (error) {
      console.error('Failed to load collection:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderItem = ({ item }: { item: CollectionItem }) => (
    <View style={styles.itemCard}>
      <View style={styles.itemIcon}>
        <Ionicons 
          name={item.rarity === 'legendary' ? 'star' : item.rarity === 'epic' ? 'diamond' : item.rarity === 'rare' ? 'trophy' : 'cube'} 
          size={32} 
          color={item.rarity === 'legendary' ? '#FFD700' : item.rarity === 'epic' ? '#9B59B6' : item.rarity === 'rare' ? '#3498DB' : '#95A5A6'} 
        />
      </View>
      <Text style={styles.itemName}>{item.name}</Text>
      <Text style={styles.itemRarity}>{item.rarity.toUpperCase()}</Text>
      {item.description && <Text style={styles.itemDescription}>{item.description}</Text>}
    </View>
  );

  return (
    <LinearGradient colors={Gradients.background} style={styles.container}>
      <StatusBar style="light" />
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => navigation.goBack()}
            activeOpacity={0.7}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>My Collection</Text>
          <View style={styles.headerSpacer} />
        </View>

        {/* Content */}
        <View style={styles.content}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Loading collection...</Text>
            </View>
          ) : items.length > 0 ? (
            <FlatList
              data={items}
              renderItem={renderItem}
              keyExtractor={(item) => item.id.toString()}
              numColumns={2}
              contentContainerStyle={styles.listContainer}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <View style={styles.emptyContainer}>
              <Ionicons name="library-outline" size={64} color={Colors.white} />
              <Text style={styles.emptyTitle}>No Items Yet</Text>
              <Text style={styles.emptyText}>
                Start shopping to build your collection!
              </Text>
            </View>
          )}
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    color: Colors.white,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingTop: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.white,
    fontWeight: '500',
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  itemCard: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    padding: 16,
    margin: 8,
    flex: 1,
    alignItems: 'center',
    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  itemIcon: {
    marginBottom: 12,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.black,
    textAlign: 'center',
    marginBottom: 4,
  },
  itemRarity: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666666',
    marginBottom: 8,
  },
  itemDescription: {
    fontSize: 12,
    color: '#888888',
    textAlign: 'center',
    lineHeight: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.white,
    marginTop: 20,
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
});
