import type { ObjectSchema, PropertySchema, RealmObjectConstructor } from "../schema";
import type { Configuration } from "../Configuration";
/**
 * Validate the data types of the fields of a user-provided realm schema.
 */
export declare function validateRealmSchema(realmSchema: unknown): asserts realmSchema is Configuration["schema"][];
/**
 * Validate the data types of the fields of a user-provided object schema.
 */
export declare function validateObjectSchema(objectSchema: unknown): asserts objectSchema is RealmObjectConstructor | ObjectSchema;
/**
 * Validate the data types of a user-provided property schema that ought to use the
 * relaxed object notation.
 */
export declare function validatePropertySchema(objectName: string, propertyName: string, propertySchema: unknown): asserts propertySchema is PropertySchema;
