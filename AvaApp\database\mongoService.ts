// Direct MongoDB Connection Service for React Native
// This service connects to your local MongoDB database at localhost:27017/test/catalog

export interface CatalogItem {
  _id?: string;
  name: string;
  description?: string;
  image_url?: string;
  price?: number;
  category?: string;
  rarity?: string;
  set_name?: string;
  card_number?: string;
  artist?: string;
  type?: string;
  mana_cost?: string;
  power?: string;
  toughness?: string;
  text?: string;
  flavor_text?: string;
  created_at?: Date;
  updated_at?: Date;
}

class MongoService {
  private isConnected: boolean = false;
  private baseUrl: string = 'http://************:27017'; // Direct MongoDB HTTP interface
  private databaseName = 'test';
  private collectionName = 'catalog';

  // Alternative: Use MongoDB REST API or create a simple bridge server
  private mongoApiUrl = 'http://************:3001/api'; // Our existing API server

  async connect(): Promise<boolean> {
    try {
      console.log('Connecting to MongoDB via API bridge...');

      // Test connection to our API server that's connected to MongoDB
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`${this.mongoApiUrl}/catalog/count`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        this.isConnected = true;
        console.log(`Successfully connected to MongoDB! Catalog has ${data.count} items`);
        return true;
      } else {
        console.error('Failed to connect to MongoDB API:', response.status);
        this.isConnected = false;
        return false;
      }
    } catch (error) {
      console.error('Failed to connect to MongoDB:', error);
      this.isConnected = false;
      return false;
    }
  }

  async disconnect(): Promise<void> {
    this.isConnected = false;
    console.log('Disconnected from MongoDB API');
  }

  async searchCatalog(query: string, limit: number = 50): Promise<CatalogItem[]> {
    if (!this.isConnected) {
      console.log('MongoDB not connected, cannot search');
      return [];
    }

    try {
      console.log(`Searching MongoDB for: "${query}"`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(`${this.mongoApiUrl}/catalog/search?q=${encodeURIComponent(query)}&limit=${limit}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        console.log(`MongoDB search found ${data.items.length} results`);

        // Convert to our format
        const formattedResults = data.items.map((item: any) => ({
          ...item,
          mongo_id: item._id?.toString() || item.mongo_id,
          _id: undefined // Remove the ObjectId
        }));

        return formattedResults;
      } else {
        console.error('MongoDB search failed:', response.status);
        return [];
      }
    } catch (error) {
      console.error('MongoDB search error:', error);
      return [];
    }
  }

  async getCatalogItemCount(): Promise<number> {
    if (!this.isConnected) {
      return 0;
    }

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`${this.mongoApiUrl}/catalog/count`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        return data.count;
      } else {
        console.error('Error getting catalog count:', response.status);
        return 0;
      }
    } catch (error) {
      console.error('Error getting catalog count:', error);
      return 0;
    }
  }

  async getCatalogItemsByCategory(category: string, limit: number = 100): Promise<CatalogItem[]> {
    if (!this.isConnected || !this.catalogCollection) {
      return [];
    }

    try {
      const results = await this.catalogCollection
        .find({ category: { $regex: new RegExp(category, 'i') } })
        .limit(limit)
        .toArray();

      return results.map(item => ({
        ...item,
        mongo_id: item._id?.toString(),
        _id: undefined
      }));
    } catch (error) {
      console.error('Error getting items by category:', error);
      return [];
    }
  }

  async getCatalogItemsBySet(setName: string, limit: number = 100): Promise<CatalogItem[]> {
    if (!this.isConnected || !this.catalogCollection) {
      return [];
    }

    try {
      const results = await this.catalogCollection
        .find({ set_name: { $regex: new RegExp(setName, 'i') } })
        .limit(limit)
        .toArray();

      return results.map(item => ({
        ...item,
        mongo_id: item._id?.toString(),
        _id: undefined
      }));
    } catch (error) {
      console.error('Error getting items by set:', error);
      return [];
    }
  }

  async getRandomCatalogItems(limit: number = 20): Promise<CatalogItem[]> {
    if (!this.isConnected || !this.catalogCollection) {
      return [];
    }

    try {
      const results = await this.catalogCollection
        .aggregate([
          { $sample: { size: limit } }
        ])
        .toArray();

      return results.map(item => ({
        ...item,
        mongo_id: item._id?.toString(),
        _id: undefined
      }));
    } catch (error) {
      console.error('Error getting random items:', error);
      return [];
    }
  }

  async getAllCategories(): Promise<string[]> {
    if (!this.isConnected || !this.catalogCollection) {
      return [];
    }

    try {
      const categories = await this.catalogCollection.distinct('category');
      return categories.filter(cat => cat && cat.trim() !== '');
    } catch (error) {
      console.error('Error getting categories:', error);
      return [];
    }
  }

  async getAllSets(): Promise<string[]> {
    if (!this.isConnected || !this.catalogCollection) {
      return [];
    }

    try {
      const sets = await this.catalogCollection.distinct('set_name');
      return sets.filter(set => set && set.trim() !== '');
    } catch (error) {
      console.error('Error getting sets:', error);
      return [];
    }
  }

  isMongoConnected(): boolean {
    return this.isConnected;
  }
}

// Export singleton instance
export const mongoService = new MongoService();
