"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_v8_1 = __importDefault(require("node:v8"));
const node_vm_1 = __importDefault(require("node:vm"));
const garbage_collection_1 = require("../garbage-collection");
(0, garbage_collection_1.inject)({
    collect() {
        // Ensure we have the gc function available
        node_v8_1.default.setFlagsFromString("--expose_gc");
        const gc = node_vm_1.default.runInNewContext("gc");
        // Garbage collect
        process.nextTick(gc);
    },
});
//# sourceMappingURL=garbage-collection.js.map