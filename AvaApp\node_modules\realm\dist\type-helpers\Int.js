"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.createIntTypeHelpers = void 0;
const binding_1 = require("../binding");
const errors_1 = require("../errors");
const Counter_1 = require("../Counter");
const null_passthrough_1 = require("./null-passthrough");
/** @internal */
function createIntTypeHelpers({ presentation, optional }) {
    return {
        toBinding: (0, null_passthrough_1.nullPassthrough)((value) => {
            if (typeof value === "number") {
                return binding_1.binding.Int64.numToInt(value);
            }
            else if (binding_1.binding.Int64.isInt(value)) {
                return value;
            }
            else if (value instanceof Counter_1.Counter) {
                if (presentation !== "counter") {
                    throw new Error(`Counters can only be used when 'counter' is declared in the property schema.`);
                }
                return binding_1.binding.Int64.numToInt(value.value);
            }
            else {
                throw new errors_1.TypeAssertionError("a number or bigint", value);
            }
        }, optional),
        // TODO: Support returning bigints to end-users
        fromBinding: (0, null_passthrough_1.nullPassthrough)((value) => Number(value), optional),
    };
}
exports.createIntTypeHelpers = createIntTypeHelpers;
//# sourceMappingURL=Int.js.map