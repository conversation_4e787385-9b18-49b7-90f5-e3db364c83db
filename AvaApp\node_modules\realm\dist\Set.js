"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2022 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.RealmSet = void 0;
const binding_1 = require("./binding");
const assert_1 = require("./assert");
const errors_1 = require("./errors");
const indirect_1 = require("./indirect");
const Collection_1 = require("./Collection");
const OrderedCollection_1 = require("./OrderedCollection");
/**
 * Instances of this class will be returned when accessing object properties whose type is `"Set"`
 *
 * Sets mostly behave like normal JavaScript Sets, with a few exceptions:
 * They can only store values of a single type (indicated by the `type`
 * and `optional` properties of the Set).
 * They can only be modified inside a **write** transaction.
 * Unlike JavaScript's Set, Realm~Set does NOT make any guarantees about the
 * traversal order of `values()`, `entries()`, `keys()`, or `forEach` iterations.
 * If values in a Set are required to have some order, it must be implemented
 * by the developer by, for example, wrapping values in an object that holds
 * a user-supplied insertion order.
 * @see https://www.mongodb.com/docs/realm/sdk/react-native/model-data/data-types/sets/
 */
class RealmSet extends OrderedCollection_1.OrderedCollection {
    /** @internal */
    constructor(realm, internal, accessor, typeHelpers) {
        if (arguments.length === 0 || !(internal instanceof binding_1.binding.Set)) {
            throw new errors_1.IllegalConstructorError("Set");
        }
        super(realm, internal.asResults(), accessor, typeHelpers);
        Object.defineProperty(this, "internal", {
            enumerable: false,
            configurable: false,
            writable: false,
            value: internal,
        });
    }
    /** @internal */
    get(index) {
        return this[Collection_1.COLLECTION_ACCESSOR].get(this.internal, index);
    }
    /** @internal */
    set(index, value) {
        this[Collection_1.COLLECTION_ACCESSOR].set(this.internal, index, value);
    }
    /**
     * @returns The number of values in the Set.
     */
    get size() {
        return this.length;
    }
    /**
     * Checks if this Set has not been deleted and is part of a valid Realm.
     * @returns `true` if the set can be safely accessed, `false` if not.
     */
    isValid() {
        return this.internal.isValid;
    }
    /**
     * Delete a value from the Set.
     * @param value - Value to delete from the Set.
     * @throws An {@link Error} if not inside a write transaction.
     * @returns `true` if the value existed in the Set prior to deletion, `false` if not.
     */
    delete(value) {
        assert_1.assert.inTransaction(this.realm);
        const [, success] = this.internal.removeAny(this[Collection_1.COLLECTION_TYPE_HELPERS].toBinding(value));
        return success;
    }
    /**
     * Add a new value to the Set.
     * @param value - Value to add to the Set.
     * @throws A {@link TypeError} if a `value` is not of a type which can be stored in
     * the Set, or if an object being added to the Set does not match the for the Set.
     * @throws An {@link Error} if not inside a write transaction.
     * @returns The Set itself, after adding the new value.
     */
    add(value) {
        this[Collection_1.COLLECTION_ACCESSOR].insert(this.internal, value);
        return this;
    }
    /**
     * Remove all values from the Set.
     * @throws An {@link Error} if not inside a write transaction.
     */
    clear() {
        assert_1.assert.inTransaction(this.realm);
        this.internal.deleteAll();
    }
    /**
     * Check for existence of a value in the Set.
     * @param value - Value to search for in the Set
     * @throws A {@link TypeError} if a `value` is not of a type which can be stored in
     * the Set, or if an object being added to the Set does not match the
     * **object schema** for the Set.
     * @returns `true` if the value exists in the Set, `false` if not.
     */
    has(value) {
        return this.includes(value);
    }
    /**
     * @see {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Set/entries | Set.prototype.entries()}
     * @returns An iterator over the entries of the Set. Each entry is a two-element array
     * containing the value from the Set, followed by a copy of the same value (`[value, value]`).
     */
    *entries() {
        for (const value of this.values()) {
            yield [value, value];
        }
    }
}
exports.RealmSet = RealmSet;
(0, indirect_1.injectIndirect)("Set", RealmSet);
//# sourceMappingURL=Set.js.map