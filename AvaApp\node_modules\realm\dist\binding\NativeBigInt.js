"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.NativeBigInt = void 0;
const assert_1 = require("../assert");
class NativeBigInt {
    static add(a, b) {
        (0, assert_1.assert)(typeof a === "bigint");
        (0, assert_1.assert)(typeof b === "bigint");
        return (a + b);
    }
    static equals(a, b) {
        (0, assert_1.assert)(typeof a === "bigint");
        (0, assert_1.assert)(typeof b === "bigint" || typeof b === "number" || typeof b === "string");
        return a == b; // using == rather than === to support number and string RHS!
    }
    static isInt(a) {
        return typeof a === "bigint";
    }
    static numToInt(a) {
        return BigInt(a);
    }
    static strToInt(a) {
        return BigInt(a);
    }
    static intToNum(a) {
        return Number(a);
    }
}
exports.NativeBigInt = NativeBigInt;
//# sourceMappingURL=NativeBigInt.js.map