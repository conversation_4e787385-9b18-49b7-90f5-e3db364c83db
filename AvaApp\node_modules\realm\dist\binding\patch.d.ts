/** @internal */
import type { binding } from "./wrapper.generated";
type Binding = typeof binding;
/** @internal */
declare module "./wrapper.generated" {
    namespace binding {
        /** @internal */
        interface IndexSet {
            asIndexes(): Iterator<number>;
        }
        interface Timestamp {
            toDate(): Date;
        }
        namespace Timestamp {
            function fromDate(d: Date): binding.Timestamp;
        }
        class InvalidObjKey extends TypeError {
            constructor(input: string);
        }
        function stringToObjKey(input: string): binding.ObjKey;
        function isEmptyObjKey(objKey: binding.ObjKey): boolean;
    }
}
/**
 * Applies SDK level patches to the binding.
 * This should only be called after the binding has been injected.
 * @internal
 */
export declare function applyPatch(binding: Binding): void;
export {};
