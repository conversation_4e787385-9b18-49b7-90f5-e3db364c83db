"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.isJsOrRealmList = exports.insertIntoListOfMixed = exports.createListAccessor = void 0;
const binding_1 = require("../binding");
const assert_1 = require("../assert");
const indirect_1 = require("../indirect");
const Dictionary_1 = require("./Dictionary");
const OrderedCollection_1 = require("./OrderedCollection");
/** @internal */
function createListAccessor(options) {
    return options.itemType === 9 /* binding.PropertyType.Mixed */
        ? createListAccessorForMixed(options)
        : createListAccessorForKnownType(options);
}
exports.createListAccessor = createListAccessor;
function createListAccessorForMixed({ realm, typeHelpers, }) {
    const { toBinding } = typeHelpers;
    return {
        get(list, index) {
            const value = list.getAny(index);
            switch (value) {
                case binding_1.binding.ListSentinel: {
                    const accessor = createListAccessor({ realm, typeHelpers, itemType: 9 /* binding.PropertyType.Mixed */ });
                    return new indirect_1.indirect.List(realm, list.getList(index), accessor, typeHelpers);
                }
                case binding_1.binding.DictionarySentinel: {
                    const accessor = (0, Dictionary_1.createDictionaryAccessor)({ realm, typeHelpers, itemType: 9 /* binding.PropertyType.Mixed */ });
                    return new indirect_1.indirect.Dictionary(realm, list.getDictionary(index), accessor, typeHelpers);
                }
                default:
                    return typeHelpers.fromBinding(value);
            }
        },
        set(list, index, value) {
            assert_1.assert.inTransaction(realm);
            if (isJsOrRealmList(value)) {
                list.setCollection(index, 19 /* binding.CollectionType.List */);
                insertIntoListOfMixed(value, list.getList(index), toBinding);
            }
            else if ((0, Dictionary_1.isJsOrRealmDictionary)(value)) {
                list.setCollection(index, 21 /* binding.CollectionType.Dictionary */);
                (0, Dictionary_1.insertIntoDictionaryOfMixed)(value, list.getDictionary(index), toBinding);
            }
            else {
                list.setAny(index, toBinding(value));
            }
        },
        insert(list, index, value) {
            assert_1.assert.inTransaction(realm);
            if (isJsOrRealmList(value)) {
                list.insertCollection(index, 19 /* binding.CollectionType.List */);
                insertIntoListOfMixed(value, list.getList(index), toBinding);
            }
            else if ((0, Dictionary_1.isJsOrRealmDictionary)(value)) {
                list.insertCollection(index, 21 /* binding.CollectionType.Dictionary */);
                (0, Dictionary_1.insertIntoDictionaryOfMixed)(value, list.getDictionary(index), toBinding);
            }
            else {
                list.insertAny(index, toBinding(value));
            }
        },
    };
}
function createListAccessorForKnownType({ realm, typeHelpers, itemType, isEmbedded, }) {
    const { fromBinding, toBinding } = typeHelpers;
    return {
        get: (0, OrderedCollection_1.createDefaultGetter)({ fromBinding, itemType }),
        set(list, index, value) {
            assert_1.assert.inTransaction(realm);
            list.setAny(index, toBinding(value, isEmbedded ? { createObj: () => [list.setEmbedded(index), true] } : undefined));
        },
        insert(list, index, value) {
            assert_1.assert.inTransaction(realm);
            if (isEmbedded) {
                // Simply transforming to binding will insert the embedded object
                toBinding(value, { createObj: () => [list.insertEmbedded(index), true] });
            }
            else {
                list.insertAny(index, toBinding(value));
            }
        },
    };
}
/** @internal */
function insertIntoListOfMixed(list, internal, toBinding) {
    // TODO: Solve the "removeAll()" case for self-assignment (https://github.com/realm/realm-core/issues/7422).
    internal.removeAll();
    for (const [index, item] of list.entries()) {
        if (isJsOrRealmList(item)) {
            internal.insertCollection(index, 19 /* binding.CollectionType.List */);
            insertIntoListOfMixed(item, internal.getList(index), toBinding);
        }
        else if ((0, Dictionary_1.isJsOrRealmDictionary)(item)) {
            internal.insertCollection(index, 21 /* binding.CollectionType.Dictionary */);
            (0, Dictionary_1.insertIntoDictionaryOfMixed)(item, internal.getDictionary(index), toBinding);
        }
        else {
            internal.insertAny(index, toBinding(item));
        }
    }
}
exports.insertIntoListOfMixed = insertIntoListOfMixed;
/** @internal */
function isJsOrRealmList(value) {
    return Array.isArray(value) || value instanceof indirect_1.indirect.List;
}
exports.isJsOrRealmList = isJsOrRealmList;
//# sourceMappingURL=List.js.map