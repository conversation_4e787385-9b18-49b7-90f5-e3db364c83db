#!/usr/bin/env python3
"""
One-time script to sync catalog data from MongoDB to SQLite
Pulls all records from mongodb://localhost:27017/test/catalog into SQLite
"""

import sqlite3
import pymongo
from datetime import datetime
import json
import os

# Database configurations
MONGO_URI = "mongodb://localhost:27017/"
MONGO_DB = "test"
MONGO_COLLECTION = "catalog"
SQLITE_DB_PATH = "../ava_app.db"  # Relative to script location

def connect_mongodb():
    """Connect to MongoDB and return collection"""
    try:
        client = pymongo.MongoClient(MONGO_URI)
        db = client[MONGO_DB]
        collection = db[MONGO_COLLECTION]
        print(f"Connected to MongoDB: {MONGO_URI}")
        return collection
    except Exception as e:
        print(f"Failed to connect to MongoDB: {e}")
        return None

def connect_sqlite():
    """Connect to SQLite and create catalog table if not exists"""
    try:
        # Get the directory where the script is located
        script_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(script_dir, SQLITE_DB_PATH)
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create catalog_items table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS catalog_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                mongo_id TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                image_url TEXT,
                price REAL,
                category TEXT,
                rarity TEXT,
                set_name TEXT,
                card_number TEXT,
                artist TEXT,
                type TEXT,
                mana_cost TEXT,
                power TEXT,
                toughness TEXT,
                text TEXT,
                flavor_text TEXT,
                raw_data TEXT,  -- Store original MongoDB document as JSON
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create indexes for better search performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_catalog_name ON catalog_items(name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_catalog_category ON catalog_items(category)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_catalog_set ON catalog_items(set_name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_catalog_rarity ON catalog_items(rarity)')
        
        conn.commit()
        print(f"Connected to SQLite: {db_path}")
        return conn
    except Exception as e:
        print(f"Failed to connect to SQLite: {e}")
        return None

def extract_field(doc, field_name, default=None):
    """Safely extract field from MongoDB document"""
    return doc.get(field_name, default)

def sync_catalog_data():
    """Main function to sync data from MongoDB to SQLite"""
    # Connect to databases
    mongo_collection = connect_mongodb()
    sqlite_conn = connect_sqlite()
    
    if mongo_collection is None or sqlite_conn is None:
        print("Failed to connect to databases")
        return
    
    try:
        # Get total count from MongoDB
        total_count = mongo_collection.count_documents({})
        print(f"Found {total_count} documents in MongoDB catalog")
        
        if total_count == 0:
            print("No documents found in MongoDB catalog")
            return
        
        # Clear existing catalog data (optional - remove if you want to keep existing data)
        cursor = sqlite_conn.cursor()
        cursor.execute("DELETE FROM catalog_items")
        print("Cleared existing catalog data from SQLite")
        
        # Fetch all documents from MongoDB
        documents = mongo_collection.find({})
        
        inserted_count = 0
        error_count = 0
        
        for doc in documents:
            try:
                # Extract MongoDB _id as string
                mongo_id = str(doc.get('_id', ''))
                
                # Extract common fields (adjust based on your actual MongoDB schema)
                name = extract_field(doc, 'name', '')
                description = extract_field(doc, 'description')
                image_url = extract_field(doc, 'image_url') or extract_field(doc, 'imageUrl')
                price = extract_field(doc, 'price')
                category = extract_field(doc, 'category') or extract_field(doc, 'type')
                rarity = extract_field(doc, 'rarity')
                set_name = extract_field(doc, 'set_name') or extract_field(doc, 'setName') or extract_field(doc, 'set')
                card_number = extract_field(doc, 'card_number') or extract_field(doc, 'cardNumber') or extract_field(doc, 'number')
                artist = extract_field(doc, 'artist')
                card_type = extract_field(doc, 'type') or extract_field(doc, 'cardType')
                mana_cost = extract_field(doc, 'mana_cost') or extract_field(doc, 'manaCost') or extract_field(doc, 'cost')
                power = extract_field(doc, 'power')
                toughness = extract_field(doc, 'toughness')
                text = extract_field(doc, 'text') or extract_field(doc, 'cardText')
                flavor_text = extract_field(doc, 'flavor_text') or extract_field(doc, 'flavorText')
                
                # Store raw MongoDB document as JSON for reference
                raw_data = json.dumps(doc, default=str)
                
                # Convert price to float if it's a string
                if isinstance(price, str):
                    try:
                        price = float(price)
                    except (ValueError, TypeError):
                        price = None
                
                # Insert into SQLite
                cursor.execute('''
                    INSERT OR REPLACE INTO catalog_items (
                        mongo_id, name, description, image_url, price, category, rarity,
                        set_name, card_number, artist, type, mana_cost, power, toughness,
                        text, flavor_text, raw_data, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (
                    mongo_id, name, description, image_url, price, category, rarity,
                    set_name, card_number, artist, card_type, mana_cost, power, toughness,
                    text, flavor_text, raw_data
                ))
                
                inserted_count += 1
                
                # Progress indicator
                if inserted_count % 100 == 0:
                    print(f"Processed {inserted_count}/{total_count} documents...")
                    
            except Exception as e:
                error_count += 1
                print(f"Error processing document {doc.get('_id', 'unknown')}: {e}")
                continue
        
        # Commit all changes
        sqlite_conn.commit()
        
        # Print summary
        print(f"\n=== Sync Complete ===")
        print(f"Total documents in MongoDB: {total_count}")
        print(f"Successfully inserted: {inserted_count}")
        print(f"Errors: {error_count}")
        
        # Verify SQLite data
        cursor.execute("SELECT COUNT(*) FROM catalog_items")
        sqlite_count = cursor.fetchone()[0]
        print(f"Total records in SQLite: {sqlite_count}")
        
        # Show sample data
        cursor.execute("SELECT name, category, set_name FROM catalog_items LIMIT 5")
        sample_data = cursor.fetchall()
        print(f"\nSample data:")
        for row in sample_data:
            print(f"  - {row[0]} ({row[1]}) from {row[2]}")
            
    except Exception as e:
        print(f"Error during sync: {e}")
    finally:
        sqlite_conn.close()
        print("Database connections closed")

if __name__ == "__main__":
    print("Starting MongoDB to SQLite catalog sync...")
    sync_catalog_data()
    print("Sync completed!")
