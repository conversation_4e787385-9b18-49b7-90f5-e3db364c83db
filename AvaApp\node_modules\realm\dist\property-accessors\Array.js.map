{"version": 3, "file": "Array.js", "sourceRoot": "", "sources": ["../../src/property-accessors/Array.ts"], "names": [], "mappings": ";AAAA,4EAA4E;AAC5E,EAAE;AACF,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,6CAA6C;AAC7C,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,EAAE;AACF,4EAA4E;;;AAE5E,wCAAqC;AACrC,sCAAmC;AACnC,uDAAkE;AAClE,6DAAwE;AACxE,sCAA+C;AAC/C,kCAA+B;AAC/B,wCAAqC;AACrC,gDAA4D;AAG5D,gBAAgB;AAChB,SAAgB,2BAA2B,CAAC,EAC1C,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,UAAU,EACV,QAAQ,EACR,sBAAsB,EACtB,eAAe,EACf,QAAQ,GACQ;IAChB,MAAM,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC;IACrC,MAAM,QAAQ,GAAG,IAAA,wBAAU,EAAC,IAAI,CAAC,CAAC;IAClC,MAAM,WAAW,GAAG,IAAA,4BAAc,EAAC,QAAQ,EAAE;QAC3C,KAAK;QACL,IAAI,EAAE,cAAc,IAAI,EAAE;QAC1B,QAAQ;QACR,eAAe;QACf,UAAU;QACV,gBAAgB,EAAE,SAAS;KAC5B,CAAC,CAAC;IAEH,IAAI,QAAQ,gDAAwC,EAAE;QACpD,0CAA0C;QAC1C,eAAM,CAAC,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QACzC,IAAA,eAAM,EAAC,UAAU,KAAK,EAAE,EAAE,6BAA6B,CAAC,CAAC;QACzD,MAAM,kBAAkB,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;QACvD,MAAM,EACJ,YAAY,EAAE,EAAE,QAAQ,EAAE,mBAAmB,EAAE,GAChD,GAAG,kBAAkB,CAAC;QACvB,2EAA2E;QAC3E,MAAM,cAAc,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,sBAAsB,CAAC,CAAC;QAC1F,IAAA,eAAM,EAAC,cAAc,EAAE,eAAe,sBAAsB,iBAAiB,UAAU,EAAE,CAAC,CAAC;QAC3F,MAAM,QAAQ,GAAG,iBAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QACnE,MAAM,eAAe,GAAG,IAAA,+BAAqB,EAAC,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC,CAAC;QAE7F,OAAO;YACL,GAAG,CAAC,GAAgB;gBAClB,MAAM,SAAS,GAAG,GAAG,CAAC,eAAe,CAAC,QAAQ,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC;gBAC1E,MAAM,OAAO,GAAG,iBAAO,CAAC,OAAO,CAAC,aAAa,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;gBACxE,OAAO,IAAI,iBAAO,CAAC,KAAK,EAAE,OAAO,EAAE,eAAe,EAAE,WAAW,CAAC,CAAC;YACnE,CAAC;YACD,GAAG;gBACD,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;YACnC,CAAC;SACF,CAAC;KACH;SAAM;QACL,MAAM,YAAY,GAAG,IAAA,yBAAkB,EAAC,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC;QAE7G,OAAO;YACL,YAAY;YACZ,GAAG,CAAC,GAAgB;gBAClB,MAAM,QAAQ,GAAG,iBAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;gBACnE,eAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,iBAAO,CAAC,IAAI,CAAC,CAAC;gBAC1C,OAAO,IAAI,WAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;YAC9D,CAAC;YACD,GAAG,CAAC,GAAG,EAAE,MAAM;gBACb,eAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC5B,eAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAExB,MAAM,QAAQ,GAAG,iBAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;gBACnE,QAAQ,CAAC,SAAS,EAAE,CAAC;gBACrB,IAAI,KAAK,GAAG,CAAC,CAAC;gBACd,IAAI;oBACF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;wBAC1B,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;qBAC/C;iBACF;gBAAC,OAAO,GAAG,EAAE;oBACZ,IAAI,GAAG,YAAY,2BAAkB,EAAE;wBACrC,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;qBACrC;oBACD,MAAM,GAAG,CAAC;iBACX;YACH,CAAC;SACF,CAAC;KACH;AACH,CAAC;AA5ED,kEA4EC"}