"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSetPropertyAccessor = void 0;
const binding_1 = require("../binding");
const assert_1 = require("../assert");
const Set_1 = require("../Set");
const TypeHelpers_1 = require("../TypeHelpers");
const Set_2 = require("../collection-accessors/Set");
/** @internal */
function createSetPropertyAccessor({ columnKey, realm, name, type, optional, objectType, getClassHelpers, }) {
    const itemType = (0, TypeHelpers_1.toItemType)(type);
    const itemHelpers = (0, TypeHelpers_1.getTypeHelpers)(itemType, {
        realm,
        name: `value in ${name}`,
        getClassHelpers,
        objectType,
        optional,
        objectSchemaName: undefined,
    });
    assert_1.assert.string(objectType);
    const setAccessor = (0, Set_2.createSetAccessor)({ realm, typeHelpers: itemHelpers, itemType });
    return {
        get(obj) {
            const internal = binding_1.binding.Set.make(realm.internal, obj, columnKey);
            return new Set_1.RealmSet(realm, internal, setAccessor, itemHelpers);
        },
        set(obj, value) {
            assert_1.assert.inTransaction(realm);
            const internal = binding_1.binding.Set.make(realm.internal, obj, columnKey);
            // Clear the set before adding new values
            internal.removeAll();
            assert_1.assert.array(value, "values");
            for (const v of value) {
                setAccessor.insert(internal, v);
            }
        },
    };
}
exports.createSetPropertyAccessor = createSetPropertyAccessor;
//# sourceMappingURL=Set.js.map