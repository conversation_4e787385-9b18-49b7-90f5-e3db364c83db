{"version": 3, "file": "ProgressRealmPromise.js", "sourceRoot": "", "sources": ["../src/ProgressRealmPromise.ts"], "names": [], "mappings": ";AAAA,4EAA4E;AAC5E,EAAE;AACF,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,6CAA6C;AAC7C,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,EAAE;AACF,4EAA4E;;;AAE5E,uCAAoC;AACpC,mCAAgC;AAChC,yCAAsC;AACtC,mDAA4E;AAC5E,mDAAgD;AAIhD,MAAa,oBAAoB;IAC/B,gBAAgB;IACR,MAAM,CAAC,SAAS,GAAG,IAAI,GAAG,EAAyC,CAAC;IAC5E;;;OAGG;IACI,MAAM,CAAC,SAAS;QACrB,KAAK,MAAM,UAAU,IAAI,oBAAoB,CAAC,SAAS,EAAE;YACvD,UAAU,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC;SAC9B;QACD,oBAAoB,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IACzC,CAAC;IACD,gBAAgB;IACR,MAAM,GAAG,IAAI,6BAAa,EAAS,CAAC;IAC5C,gBAAgB;IACR,cAAc,GAAiC,IAAI,CAAC;IAC5D;;;OAGG;IACK,aAAa,GAAS,IAAI,CAAC;IAEnC,gBAAgB;IAChB,YAAY,MAAqB;QAC/B,IAAI,aAAK,CAAC,sBAAsB,EAAE;YAChC,oBAAoB,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,iBAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;SAC/D;QACD,IAAI;YACF,IAAA,qCAAqB,EAAC,MAAM,CAAC,CAAC;YAC9B,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACzC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC5B;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;gBAC/B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;aAC3B;YACD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACzB;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM;QACJ,IAAI,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC;QAC9B,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;YAC/B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC3B;QACD,qCAAqC;QACrC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC1D,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC5D,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAEhE,gBAAgB;IACR,gBAAgB;QACtB,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QAC7C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QACtB,OAAO,oBAAoB,CAAC,IAAI,CAAC;IACnC,CAAC;;AAlEU,oDAAoB"}