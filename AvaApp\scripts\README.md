# Catalog Sync Script

This script pulls all records from MongoDB and inserts them into the SQLite database for the AvaApp.

## Prerequisites

1. **Python 3.7+** installed
2. **MongoDB** running on `localhost:27017`
3. **Database**: `test`
4. **Collection**: `catalog`

## Setup

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

## Usage

1. Make sure MongoDB is running and accessible at `mongodb://localhost:27017/`
2. Ensure the `test` database has a `catalog` collection with data
3. Run the sync script:

```bash
python sync_catalog.py
```

## What it does

1. **Connects** to MongoDB (`localhost:27017/test/catalog`)
2. **Creates** SQLite table `catalog_items` if it doesn't exist
3. **Clears** existing catalog data (optional)
4. **Fetches** all documents from MongoDB
5. **Inserts** them into SQLite with proper field mapping
6. **Creates** search indexes for better performance

## Database Schema

The script creates a `catalog_items` table with these fields:

- `id` - Auto-increment primary key
- `mongo_id` - Original MongoDB _id
- `name` - Card/item name
- `description` - Description
- `image_url` - Image URL
- `price` - Price (if available)
- `category` - Category/type
- `rarity` - Rarity level
- `set_name` - Set name
- `card_number` - Card number
- `artist` - Artist name
- `type` - Card type
- `mana_cost` - Mana cost
- `power` - Power (for creatures)
- `toughness` - Toughness (for creatures)
- `text` - Card text
- `flavor_text` - Flavor text
- `raw_data` - Original MongoDB document as JSON
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp

## Field Mapping

The script handles various field name variations commonly found in card databases:

- `image_url` or `imageUrl`
- `set_name`, `setName`, or `set`
- `card_number`, `cardNumber`, or `number`
- `mana_cost`, `manaCost`, or `cost`
- `flavor_text` or `flavorText`
- `card_text` or `text`

## Output

The script provides progress updates and a final summary:
- Total documents processed
- Number of successful insertions
- Number of errors
- Sample data verification

## Notes

- **One-time use**: This script is designed to be run once to populate the SQLite database
- **Data preservation**: Original MongoDB documents are stored as JSON in `raw_data` field
- **Error handling**: Continues processing even if individual documents fail
- **Indexes**: Creates search indexes for name, category, set, and rarity fields
