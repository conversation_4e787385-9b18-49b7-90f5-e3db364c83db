import { binding } from "../binding";
import type { TypeHelpers } from "../TypeHelpers";
import type { Realm } from "../Realm";
/** @internal */
export type SetAccessor<T = unknown> = {
    get: (set: binding.Set, index: number) => T;
    set: (set: binding.Set, index: number, value: T) => void;
    insert: (set: binding.Set, value: T) => void;
};
type SetAccessorFactoryOptions<T> = {
    realm: Realm;
    typeHelpers: TypeHelpers<T>;
    itemType: binding.PropertyType;
};
/** @internal */
export declare function createSetAccessor<T>(options: SetAccessorFactoryOptions<T>): SetAccessor<T>;
export {};
