////////////////////////////////////////////////////////////////////////////
//
// Copyright 2017 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////

#ifndef REALM_OS_UTIL_UUID_HPP
#define REALM_OS_UTIL_UUID_HPP

#include <string>

namespace realm {
namespace util {

// Generate a random UUID and return its formatted string representation.
std::string uuid_string();

} // namespace util
} // namespace realm

#endif // REALM_OS_UTIL_UUID_HPP
