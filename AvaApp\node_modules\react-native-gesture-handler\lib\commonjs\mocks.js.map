{"version": 3, "sources": ["mocks.tsx"], "names": ["NOOP", "PanGestureHandler", "View", "attachGestureHandler", "createGestureHandler", "dropGestureHandler", "updateGestureHandler", "flushOperations", "install", "NativeViewGestureHandler", "TapGestureHandler", "ForceTouchGestureHandler", "LongPressGestureHandler", "PinchGestureHandler", "RotationGestureHandler", "FlingGestureHandler", "RawButton", "enabled", "rest", "BaseButton", "RectButton", "BorderlessButton", "TouchableNativeFeedback", "TouchableHighlight", "TouchableOpacity", "TouchableWithoutFeedback", "ScrollView", "FlatList", "Switch", "TextInput", "DrawerLayoutAndroid", "Directions", "State"], "mappings": ";;;;;;;AAAA;;AACA;;AAYA;;AACA;;;;;;AAEA,MAAMA,IAAI,GAAG,MAAM,CACjB;AACD,CAFD;;AAGA,MAAMC,iBAAiB,GAAGC,iBAA1B;AACA,MAAMC,oBAAoB,GAAGH,IAA7B;AACA,MAAMI,oBAAoB,GAAGJ,IAA7B;AACA,MAAMK,kBAAkB,GAAGL,IAA3B;AACA,MAAMM,oBAAoB,GAAGN,IAA7B;AACA,MAAMO,eAAe,GAAGP,IAAxB;AACA,MAAMQ,OAAO,GAAGR,IAAhB;AACA,MAAMS,wBAAwB,GAAGP,iBAAjC;AACA,MAAMQ,iBAAiB,GAAGR,iBAA1B;AACA,MAAMS,wBAAwB,GAAGT,iBAAjC;AACA,MAAMU,uBAAuB,GAAGV,iBAAhC;AACA,MAAMW,mBAAmB,GAAGX,iBAA5B;AACA,MAAMY,sBAAsB,GAAGZ,iBAA/B;AACA,MAAMa,mBAAmB,GAAGb,iBAA5B;;AACA,MAAMc,SAAS,GAAG,CAAC;AAAEC,EAAAA,OAAF;AAAW,KAAGC;AAAd,CAAD,kBAChB,6BAAC,oCAAD;AAAyB,EAAA,QAAQ,EAAE,CAACD;AAApC,GAAiDC,IAAjD,gBACE,6BAAC,iBAAD,OADF,CADF;;AAKA,MAAMC,UAAU,GAAGH,SAAnB;AACA,MAAMI,UAAU,GAAGJ,SAAnB;AACA,MAAMK,gBAAgB,GAAGC,oCAAzB;eAEe;AACbC,EAAAA,kBAAkB,EAAlBA,+BADa;AAEbD,EAAAA,uBAAuB,EAAvBA,oCAFa;AAGbE,EAAAA,gBAAgB,EAAhBA,6BAHa;AAIbC,EAAAA,wBAAwB,EAAxBA,qCAJa;AAKbC,EAAAA,UAAU,EAAVA,uBALa;AAMbC,EAAAA,QAAQ,EAARA,qBANa;AAObC,EAAAA,MAAM,EAANA,mBAPa;AAQbC,EAAAA,SAAS,EAATA,sBARa;AASbC,EAAAA,mBAAmB,EAAnBA,gCATa;AAUbrB,EAAAA,wBAVa;AAWbC,EAAAA,iBAXa;AAYbC,EAAAA,wBAZa;AAabC,EAAAA,uBAba;AAcbC,EAAAA,mBAda;AAebC,EAAAA,sBAfa;AAgBbC,EAAAA,mBAhBa;AAiBbC,EAAAA,SAjBa;AAkBbG,EAAAA,UAlBa;AAmBbC,EAAAA,UAnBa;AAoBbC,EAAAA,gBApBa;AAqBbpB,EAAAA,iBArBa;AAsBbE,EAAAA,oBAtBa;AAuBbC,EAAAA,oBAvBa;AAwBbC,EAAAA,kBAxBa;AAyBbC,EAAAA,oBAzBa;AA0BbC,EAAAA,eA1Ba;AA2BbC,EAAAA,OA3Ba;AA4Bb;AACAuB,EAAAA,UAAU,EAAVA,sBA7Ba;AA8BbC,EAAAA,KAAK,EAALA;AA9Ba,C", "sourcesContent": ["import React from 'react';\nimport {\n  TouchableHighlight,\n  TouchableNativeFeedback,\n  TouchableOpacity,\n  TouchableWithoutFeedback,\n  ScrollView,\n  FlatList,\n  Switch,\n  TextInput,\n  DrawerLayoutAndroid,\n  View,\n} from 'react-native';\nimport { State } from './State';\nimport { Directions } from './Directions';\n\nconst NOOP = () => {\n  // Do nothing\n};\nconst PanGestureHandler = View;\nconst attachGestureHandler = NOOP;\nconst createGestureHandler = NOOP;\nconst dropGestureHandler = NOOP;\nconst updateGestureHandler = NOOP;\nconst flushOperations = NOOP;\nconst install = NOOP;\nconst NativeViewGestureHandler = View;\nconst TapGestureHandler = View;\nconst ForceTouchGestureHandler = View;\nconst LongPressGestureHandler = View;\nconst PinchGestureHandler = View;\nconst RotationGestureHandler = View;\nconst FlingGestureHandler = View;\nconst RawButton = ({ enabled, ...rest }: any) => (\n  <TouchableNativeFeedback disabled={!enabled} {...rest}>\n    <View />\n  </TouchableNativeFeedback>\n);\nconst BaseButton = RawButton;\nconst RectButton = RawButton;\nconst BorderlessButton = TouchableNativeFeedback;\n\nexport default {\n  TouchableHighlight,\n  TouchableNativeFeedback,\n  TouchableOpacity,\n  TouchableWithoutFeedback,\n  ScrollView,\n  FlatList,\n  Switch,\n  TextInput,\n  DrawerLayoutAndroid,\n  NativeViewGestureHandler,\n  TapGestureHandler,\n  ForceTouchGestureHandler,\n  LongPressGestureHandler,\n  PinchGestureHandler,\n  RotationGestureHandler,\n  FlingGestureHandler,\n  RawButton,\n  BaseButton,\n  RectButton,\n  BorderlessButton,\n  PanGestureHandler,\n  attachGestureHandler,\n  createGestureHandler,\n  dropGestureHandler,\n  updateGestureHandler,\n  flushOperations,\n  install,\n  // Probably can be removed\n  Directions,\n  State,\n} as const;\n"]}