"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2022 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.Results = void 0;
const binding_1 = require("./binding");
const assert_1 = require("./assert");
const errors_1 = require("./errors");
const indirect_1 = require("./indirect");
const Collection_1 = require("./Collection");
const OrderedCollection_1 = require("./OrderedCollection");
/**
 * Instances of this class are typically **live** collections returned by
 * objects() that will update as new objects are either
 * added to or deleted from the Realm that match the underlying query. Results returned by
 * snapshot()}, however, will **not** live update
 * (and listener callbacks added through addListener()
 * will thus never be called).
 * @see https://www.mongodb.com/docs/realm/sdk/react-native/model-data/data-types/collections/
 */
class Results extends OrderedCollection_1.OrderedCollection {
    /** @internal */
    subscriptionName;
    /**
     * Create a `Results` wrapping a set of query `Results` from the binding.
     * @internal
     */
    constructor(realm, internal, accessor, typeHelpers) {
        if (arguments.length === 0 || !(internal instanceof binding_1.binding.Results)) {
            throw new errors_1.IllegalConstructorError("Results");
        }
        super(realm, internal, accessor, typeHelpers);
        Object.defineProperty(this, "internal", {
            enumerable: false,
            configurable: false,
            writable: false,
            value: internal,
        });
        Object.defineProperty(this, "realm", {
            enumerable: false,
            configurable: false,
            writable: false,
            value: realm,
        });
        Object.defineProperty(this, "subscriptionName", {
            enumerable: false,
            configurable: false,
            writable: true,
        });
    }
    /** @internal */
    get(index) {
        return this[Collection_1.COLLECTION_ACCESSOR].get(this.internal, index);
    }
    /** @internal */
    set() {
        throw new Error("Modifying a Results collection is not supported.");
    }
    get length() {
        return this.internal.size();
    }
    set length(value) {
        throw new Error("Cannot assign to read only property 'length'");
    }
    /*
     * @returns A string representation of the query and sorting bound to the results.
     */
    description() {
        return binding_1.binding.Helpers.getResultsDescription(this.internal);
    }
    /**
     * Bulk update objects in the collection.
     * @param propertyName - The name of the property.
     * @param value - The updated property value.
     * @throws An {@link Error} if no property with the name exists.
     * @since 2.0.0
     */
    update(propertyName, value) {
        assert_1.assert.string(propertyName);
        const { classHelpers, type, results } = this;
        (0, assert_1.assert)(type === "object" && classHelpers, "Expected a result of Objects");
        const { set: objectSet } = classHelpers.properties.get(propertyName);
        const snapshot = results.snapshot();
        const size = snapshot.size();
        for (let i = 0; i < size; i++) {
            const obj = snapshot.getObj(i);
            assert_1.assert.instanceOf(obj, binding_1.binding.Obj);
            objectSet(obj, value);
        }
    }
    /**
     * Checks if this results collection has not been deleted and is part of a valid Realm.
     * @returns `true` if the collection can be safely accessed.
     */
    isValid() {
        return this.internal.isValid;
    }
    /**
     * Checks if this collection result is empty.
     * @returns `true` if the collection result is empty, `false` if not.
     */
    isEmpty() {
        return this.internal.size() === 0;
    }
}
exports.Results = Results;
(0, indirect_1.injectIndirect)("Results", Results);
//# sourceMappingURL=Results.js.map