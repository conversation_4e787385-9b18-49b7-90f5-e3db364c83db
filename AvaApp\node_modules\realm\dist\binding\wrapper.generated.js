"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.injectNativeModule = exports.ready = exports.isReady = exports.binding = void 0;
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
// This file is generated: Update the spec instead of editing this file directly
const bson_1 = require("bson");
const utils_1 = require("./utils");
const utils = __importStar(require("./utils"));
const patch_1 = require("./patch");
// eslint-disable-next-line @typescript-eslint/no-namespace
var binding;
(function (binding) {
    binding.Float = utils.Float;
    binding.Status = utils.Status;
    binding.ListSentinel = utils.ListSentinel;
    binding.DictionarySentinel = utils.DictionarySentinel;
    // Opaque types (including Key types)
    /** Using an empty enum to express a nominal type */ let Schema;
    (function (Schema) {
    })(Schema = binding.Schema || (binding.Schema = {}));
    /** Using an empty enum to express a nominal type */ let Group;
    (function (Group) {
    })(Group = binding.Group || (binding.Group = {}));
    /** Using an empty enum to express a nominal type */ let AuditInterface;
    (function (AuditInterface) {
    })(AuditInterface = binding.AuditInterface || (binding.AuditInterface = {}));
    /** Using an empty enum to express a nominal type */ let ColKey;
    (function (ColKey) {
    })(ColKey = binding.ColKey || (binding.ColKey = {}));
    /** Using an empty enum to express a nominal type */ let ObjKey;
    (function (ObjKey) {
    })(ObjKey = binding.ObjKey || (binding.ObjKey = {}));
    /** Using an empty enum to express a nominal type */ let TableKey;
    (function (TableKey) {
    })(TableKey = binding.TableKey || (binding.TableKey = {}));
})(binding = exports.binding || (exports.binding = {}));
Object.defineProperties(binding, {
    Helpers: { get: utils_1._throwOnAccess.bind(undefined, "Helpers"), configurable: true },
    LogCategoryRef: { get: utils_1._throwOnAccess.bind(undefined, "LogCategoryRef"), configurable: true },
    Logger: { get: utils_1._throwOnAccess.bind(undefined, "Logger"), configurable: true },
    ConstTableRef: { get: utils_1._throwOnAccess.bind(undefined, "ConstTableRef"), configurable: true },
    TableRef: { get: utils_1._throwOnAccess.bind(undefined, "TableRef"), configurable: true },
    Obj: { get: utils_1._throwOnAccess.bind(undefined, "Obj"), configurable: true },
    Transaction: { get: utils_1._throwOnAccess.bind(undefined, "Transaction"), configurable: true },
    ObjectStore: { get: utils_1._throwOnAccess.bind(undefined, "ObjectStore"), configurable: true },
    Timestamp: { get: utils_1._throwOnAccess.bind(undefined, "Timestamp"), configurable: true },
    Geospatial: { get: utils_1._throwOnAccess.bind(undefined, "Geospatial"), configurable: true },
    ObjLink: { get: utils_1._throwOnAccess.bind(undefined, "ObjLink"), configurable: true },
    KeyPathMapping: { get: utils_1._throwOnAccess.bind(undefined, "KeyPathMapping"), configurable: true },
    Query: { get: utils_1._throwOnAccess.bind(undefined, "Query"), configurable: true },
    SortDescriptor: { get: utils_1._throwOnAccess.bind(undefined, "SortDescriptor"), configurable: true },
    TableView: { get: utils_1._throwOnAccess.bind(undefined, "TableView"), configurable: true },
    Results: { get: utils_1._throwOnAccess.bind(undefined, "Results"), configurable: true },
    Realm: { get: utils_1._throwOnAccess.bind(undefined, "Realm"), configurable: true },
    RealmCoordinator: { get: utils_1._throwOnAccess.bind(undefined, "RealmCoordinator"), configurable: true },
    ObjectNotifier: { get: utils_1._throwOnAccess.bind(undefined, "ObjectNotifier"), configurable: true },
    NotificationToken: { get: utils_1._throwOnAccess.bind(undefined, "NotificationToken"), configurable: true },
    IndexSet: { get: utils_1._throwOnAccess.bind(undefined, "IndexSet"), configurable: true },
    Collection: { get: utils_1._throwOnAccess.bind(undefined, "Collection"), configurable: true },
    List: { get: utils_1._throwOnAccess.bind(undefined, "List"), configurable: true },
    Set: { get: utils_1._throwOnAccess.bind(undefined, "Set"), configurable: true },
    Dictionary: { get: utils_1._throwOnAccess.bind(undefined, "Dictionary"), configurable: true },
    LoggerFactory: { get: utils_1._throwOnAccess.bind(undefined, "LoggerFactory"), configurable: true },
    ThreadSafeReference: { get: utils_1._throwOnAccess.bind(undefined, "ThreadSafeReference"), configurable: true },
    Scheduler: { get: utils_1._throwOnAccess.bind(undefined, "Scheduler"), configurable: true },
    JsPlatformHelpers: { get: utils_1._throwOnAccess.bind(undefined, "JsPlatformHelpers"), configurable: true },
});
/**
 * Is true when the native module has been injected.
 * Useful to perform asserts on platforms which inject the native module synchronously.
 */
exports.isReady = false;
// TODO: Replace with Promise.withResolvers() once it's supported on all supported platforms.
let resolveReadyPromise = () => {
    throw new Error("Expected a synchronous Promise constructor");
};
/**
 * Resolves when the native module has been injected.
 * Useful to perform asserts on platforms which inject the native module asynchronously.
 */
exports.ready = new Promise((resolve) => {
    resolveReadyPromise = resolve;
});
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function injectNativeModule(nativeModule, extras) {
    Object.assign(binding, extras);
    const _Helpers_Symbol = Symbol("Realm.Helpers.external_pointer");
    const _native_Helpers_get_table = nativeModule.Helpers_get_table;
    const _native_Helpers_get_keypath_mapping = nativeModule.Helpers_get_keypath_mapping;
    const _native_Helpers_results_append_query = nativeModule.Helpers_results_append_query;
    const _native_Helpers_make_object_notifier = nativeModule.Helpers_make_object_notifier;
    const _native_Helpers_set_binding_context = nativeModule.Helpers_set_binding_context;
    const _native_Helpers_get_or_create_object_with_primary_key = nativeModule.Helpers_get_or_create_object_with_primary_key;
    const _native_Helpers_delete_data_for_object = nativeModule.Helpers_delete_data_for_object;
    const _native_Helpers_base64_decode = nativeModule.Helpers_base64_decode;
    const _native_Helpers_make_logger_factory = nativeModule.Helpers_make_logger_factory;
    const _native_Helpers_make_logger = nativeModule.Helpers_make_logger;
    const _native_Helpers_consume_thread_safe_reference_to_shared_realm = nativeModule.Helpers_consume_thread_safe_reference_to_shared_realm;
    const _native_Helpers_file_exists = nativeModule.Helpers_file_exists;
    const _native_Helpers_get_results_description = nativeModule.Helpers_get_results_description;
    const _native_Helpers_needs_file_format_upgrade = nativeModule.Helpers_needs_file_format_upgrade;
    class Helpers {
        constructor(ptr) {
            this[_Helpers_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof Helpers))
                throw new TypeError("Expected a Helpers");
            const out = self[_Helpers_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed Helpers");
            return out;
        }
        static getTable(r, key) {
            return _native_Helpers_get_table(r, key);
        }
        static getKeypathMapping(r) {
            return _native_Helpers_get_keypath_mapping(r);
        }
        static resultsAppendQuery(results, query) {
            return _native_Helpers_results_append_query(results, query);
        }
        static makeObjectNotifier(r, o) {
            return _native_Helpers_make_object_notifier(r, o);
        }
        static setBindingContext(r, methods) {
            return _native_Helpers_set_binding_context(r, methods);
        }
        static getOrCreateObjectWithPrimaryKey(t, pk) {
            return _native_Helpers_get_or_create_object_with_primary_key(t, pk);
        }
        static deleteDataForObject(realm, object_type) {
            return _native_Helpers_delete_data_for_object(realm, object_type);
        }
        static base64Decode(input) {
            return _native_Helpers_base64_decode(input);
        }
        static makeLoggerFactory(log) {
            return _native_Helpers_make_logger_factory(log);
        }
        static makeLogger(log) {
            return _native_Helpers_make_logger(log);
        }
        static consumeThreadSafeReferenceToSharedRealm(tsr) {
            return _native_Helpers_consume_thread_safe_reference_to_shared_realm(tsr);
        }
        static fileExists(path) {
            return _native_Helpers_file_exists(path);
        }
        static getResultsDescription(results) {
            return _native_Helpers_get_results_description(results);
        }
        static needsFileFormatUpgrade(config) {
            return _native_Helpers_needs_file_format_upgrade(config);
        }
    }
    const _LogCategoryRef_Symbol = Symbol("Realm.LogCategoryRef.external_pointer");
    const _native_LogCategoryRef_set_default_level_threshold = nativeModule.LogCategoryRef_set_default_level_threshold;
    const _native_LogCategoryRef_get_category = nativeModule.LogCategoryRef_get_category;
    class LogCategoryRef {
        constructor(ptr) {
            this[_LogCategoryRef_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof LogCategoryRef))
                throw new TypeError("Expected a LogCategoryRef");
            const out = self[_LogCategoryRef_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed LogCategoryRef");
            return out;
        }
        setDefaultLevelThreshold(level) {
            return _native_LogCategoryRef_set_default_level_threshold(this[_LogCategoryRef_Symbol], level);
        }
        static getCategory(name) {
            return _native_LogCategoryRef_get_category(name);
        }
    }
    const _Logger_Symbol = Symbol("Realm.Logger.external_pointer");
    const _native_Logger_set_default_logger = nativeModule.Logger_set_default_logger;
    class Logger {
        constructor(ptr) {
            this[_Logger_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof Logger))
                throw new TypeError("Expected a Logger");
            const out = self[_Logger_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed Logger");
            return out;
        }
        static setDefaultLogger(logger) {
            return _native_Logger_set_default_logger(logger);
        }
    }
    const _ConstTableRef_Symbol = Symbol("Realm.ConstTableRef.external_pointer");
    const _native_ConstTableRef_get_column_type = nativeModule.ConstTableRef_get_column_type;
    const _native_ConstTableRef_get_link_target = nativeModule.ConstTableRef_get_link_target;
    const _native_ConstTableRef_get_object = nativeModule.ConstTableRef_get_object;
    const _native_ConstTableRef_try_get_object = nativeModule.ConstTableRef_try_get_object;
    const _native_ConstTableRef_query = nativeModule.ConstTableRef_query;
    const _native_ConstTableRef_find_primary_key = nativeModule.ConstTableRef_find_primary_key;
    const _native_ConstTableRef_get_key = nativeModule.ConstTableRef_get_key;
    const _native_ConstTableRef_Symbol_iterator = nativeModule.ConstTableRef_Symbol_iterator;
    class ConstTableRef {
        constructor(ptr) {
            this[_ConstTableRef_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof ConstTableRef))
                throw new TypeError("Expected a ConstTableRef");
            const out = self[_ConstTableRef_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed ConstTableRef");
            return out;
        }
        getColumnType(column) {
            return _native_ConstTableRef_get_column_type(this[_ConstTableRef_Symbol], column);
        }
        getLinkTarget(column) {
            return _native_ConstTableRef_get_link_target(this[_ConstTableRef_Symbol], column);
        }
        getObject(key) {
            return _native_ConstTableRef_get_object(this[_ConstTableRef_Symbol], key);
        }
        tryGetObject(key) {
            return _native_ConstTableRef_try_get_object(this[_ConstTableRef_Symbol], key);
        }
        query(query_string, args, mapping) {
            return _native_ConstTableRef_query(this[_ConstTableRef_Symbol], query_string, args, mapping);
        }
        findPrimaryKey(pk) {
            return _native_ConstTableRef_find_primary_key(this[_ConstTableRef_Symbol], pk);
        }
        get key() {
            return _native_ConstTableRef_get_key(this[_ConstTableRef_Symbol]);
        }
        [Symbol.iterator]() {
            return _native_ConstTableRef_Symbol_iterator(this[_ConstTableRef_Symbol]);
        }
    }
    const _native_TableRef_create_object = nativeModule.TableRef_create_object;
    const _native_TableRef_remove_object = nativeModule.TableRef_remove_object;
    const _native_TableRef_get_link_target = nativeModule.TableRef_get_link_target;
    const _native_TableRef_clear = nativeModule.TableRef_clear;
    const _native_TableRef_get_primary_key_column = nativeModule.TableRef_get_primary_key_column;
    class TableRef extends ConstTableRef {
        static _extract(self) {
            if (!(self instanceof TableRef))
                throw new TypeError("Expected a TableRef");
            const out = self[_ConstTableRef_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed TableRef");
            return out;
        }
        createObject() {
            return _native_TableRef_create_object(this[_ConstTableRef_Symbol]);
        }
        removeObject(key) {
            return _native_TableRef_remove_object(this[_ConstTableRef_Symbol], key);
        }
        getLinkTarget(column) {
            return _native_TableRef_get_link_target(this[_ConstTableRef_Symbol], column);
        }
        clear() {
            return _native_TableRef_clear(this[_ConstTableRef_Symbol]);
        }
        getPrimaryKeyColumn() {
            return _native_TableRef_get_primary_key_column(this[_ConstTableRef_Symbol]);
        }
    }
    const _Obj_Symbol = Symbol("Realm.Obj.external_pointer");
    const _native_Obj_get_any = nativeModule.Obj_get_any;
    const _native_Obj_set_any = nativeModule.Obj_set_any;
    const _native_Obj_set_collection = nativeModule.Obj_set_collection;
    const _native_Obj_add_int = nativeModule.Obj_add_int;
    const _native_Obj_get_linked_object = nativeModule.Obj_get_linked_object;
    const _native_Obj_get_backlink_count = nativeModule.Obj_get_backlink_count;
    const _native_Obj_get_backlink_view = nativeModule.Obj_get_backlink_view;
    const _native_Obj_create_and_set_linked_object = nativeModule.Obj_create_and_set_linked_object;
    const _native_Obj_is_valid = nativeModule.Obj_is_valid;
    const _native_Obj_get_table = nativeModule.Obj_get_table;
    const _native_Obj_get_key = nativeModule.Obj_get_key;
    class Obj {
        constructor(ptr) {
            this[_Obj_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof Obj))
                throw new TypeError("Expected a Obj");
            const out = self[_Obj_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed Obj");
            return out;
        }
        getAny(column) {
            return _native_Obj_get_any(this[_Obj_Symbol], column);
        }
        setAny(column, value) {
            return _native_Obj_set_any(this[_Obj_Symbol], column, value);
        }
        setCollection(column, type) {
            return _native_Obj_set_collection(this[_Obj_Symbol], column, type);
        }
        addInt(column, value) {
            return _native_Obj_add_int(this[_Obj_Symbol], column, value);
        }
        getLinkedObject(column) {
            return _native_Obj_get_linked_object(this[_Obj_Symbol], column);
        }
        getBacklinkCount() {
            return _native_Obj_get_backlink_count(this[_Obj_Symbol]);
        }
        getBacklinkView(src_table, src_col_key) {
            return _native_Obj_get_backlink_view(this[_Obj_Symbol], src_table, src_col_key);
        }
        createAndSetLinkedObject(column) {
            return _native_Obj_create_and_set_linked_object(this[_Obj_Symbol], column);
        }
        get isValid() {
            return _native_Obj_is_valid(this[_Obj_Symbol]);
        }
        get table() {
            return _native_Obj_get_table(this[_Obj_Symbol]);
        }
        get key() {
            return _native_Obj_get_key(this[_Obj_Symbol]);
        }
    }
    const _Transaction_Symbol = Symbol("Realm.Transaction.external_pointer");
    class Transaction {
        constructor(ptr) {
            this[_Transaction_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof Transaction))
                throw new TypeError("Expected a Transaction");
            const out = self[_Transaction_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed Transaction");
            return out;
        }
    }
    const _ObjectStore_Symbol = Symbol("Realm.ObjectStore.external_pointer");
    class ObjectStore {
        constructor(ptr) {
            this[_ObjectStore_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof ObjectStore))
                throw new TypeError("Expected a ObjectStore");
            const out = self[_ObjectStore_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed ObjectStore");
            return out;
        }
    }
    const _Timestamp_Symbol = Symbol("Realm.Timestamp.external_pointer");
    const _native_Timestamp_make = nativeModule.Timestamp_make;
    const _native_Timestamp_get_seconds = nativeModule.Timestamp_get_seconds;
    const _native_Timestamp_get_nanoseconds = nativeModule.Timestamp_get_nanoseconds;
    class Timestamp {
        constructor(ptr) {
            this[_Timestamp_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof Timestamp))
                throw new TypeError("Expected a Timestamp");
            const out = self[_Timestamp_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed Timestamp");
            return out;
        }
        static make(seconds, nanoseconds) {
            return _native_Timestamp_make(seconds, nanoseconds);
        }
        get seconds() {
            return _native_Timestamp_get_seconds(this[_Timestamp_Symbol]);
        }
        get nanoseconds() {
            return _native_Timestamp_get_nanoseconds(this[_Timestamp_Symbol]);
        }
    }
    const _Geospatial_Symbol = Symbol("Realm.Geospatial.external_pointer");
    const _native_Geospatial_make_from_circle = nativeModule.Geospatial_make_from_circle;
    const _native_Geospatial_make_from_box = nativeModule.Geospatial_make_from_box;
    const _native_Geospatial_make_from_polygon = nativeModule.Geospatial_make_from_polygon;
    class Geospatial {
        constructor(ptr) {
            this[_Geospatial_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof Geospatial))
                throw new TypeError("Expected a Geospatial");
            const out = self[_Geospatial_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed Geospatial");
            return out;
        }
        static makeFromCircle(circle) {
            return _native_Geospatial_make_from_circle(circle);
        }
        static makeFromBox(box) {
            return _native_Geospatial_make_from_box(box);
        }
        static makeFromPolygon(polygon) {
            return _native_Geospatial_make_from_polygon(polygon);
        }
    }
    const _ObjLink_Symbol = Symbol("Realm.ObjLink.external_pointer");
    const _native_ObjLink_get_table_key = nativeModule.ObjLink_get_table_key;
    const _native_ObjLink_get_obj_key = nativeModule.ObjLink_get_obj_key;
    class ObjLink {
        constructor(ptr) {
            this[_ObjLink_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof ObjLink))
                throw new TypeError("Expected a ObjLink");
            const out = self[_ObjLink_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed ObjLink");
            return out;
        }
        get tableKey() {
            return _native_ObjLink_get_table_key(this[_ObjLink_Symbol]);
        }
        get objKey() {
            return _native_ObjLink_get_obj_key(this[_ObjLink_Symbol]);
        }
    }
    const _KeyPathMapping_Symbol = Symbol("Realm.KeyPathMapping.external_pointer");
    class KeyPathMapping {
        constructor(ptr) {
            this[_KeyPathMapping_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof KeyPathMapping))
                throw new TypeError("Expected a KeyPathMapping");
            const out = self[_KeyPathMapping_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed KeyPathMapping");
            return out;
        }
    }
    const _Query_Symbol = Symbol("Realm.Query.external_pointer");
    const _native_Query_get_table = nativeModule.Query_get_table;
    const _native_Query_get_description = nativeModule.Query_get_description;
    class Query {
        constructor(ptr) {
            this[_Query_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof Query))
                throw new TypeError("Expected a Query");
            const out = self[_Query_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed Query");
            return out;
        }
        get table() {
            return _native_Query_get_table(this[_Query_Symbol]);
        }
        get description() {
            return _native_Query_get_description(this[_Query_Symbol]);
        }
    }
    const _SortDescriptor_Symbol = Symbol("Realm.SortDescriptor.external_pointer");
    class SortDescriptor {
        constructor(ptr) {
            this[_SortDescriptor_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof SortDescriptor))
                throw new TypeError("Expected a SortDescriptor");
            const out = self[_SortDescriptor_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed SortDescriptor");
            return out;
        }
    }
    const _TableView_Symbol = Symbol("Realm.TableView.external_pointer");
    class TableView {
        constructor(ptr) {
            this[_TableView_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof TableView))
                throw new TypeError("Expected a TableView");
            const out = self[_TableView_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed TableView");
            return out;
        }
    }
    const _Results_Symbol = Symbol("Realm.Results.external_pointer");
    const _native_Results_size = nativeModule.Results_size;
    const _native_Results_index_of = nativeModule.Results_index_of;
    const _native_Results_index_of_obj = nativeModule.Results_index_of_obj;
    const _native_Results_get_obj = nativeModule.Results_get_obj;
    const _native_Results_get_any = nativeModule.Results_get_any;
    const _native_Results_get_list = nativeModule.Results_get_list;
    const _native_Results_get_dictionary = nativeModule.Results_get_dictionary;
    const _native_Results_sort_by_names = nativeModule.Results_sort_by_names;
    const _native_Results_snapshot = nativeModule.Results_snapshot;
    const _native_Results_max = nativeModule.Results_max;
    const _native_Results_min = nativeModule.Results_min;
    const _native_Results_average = nativeModule.Results_average;
    const _native_Results_sum = nativeModule.Results_sum;
    const _native_Results_clear = nativeModule.Results_clear;
    const _native_Results_add_notification_callback = nativeModule.Results_add_notification_callback;
    const _native_Results_from_table = nativeModule.Results_from_table;
    const _native_Results_from_table_view = nativeModule.Results_from_table_view;
    const _native_Results_is_valid = nativeModule.Results_is_valid;
    const _native_Results_get_query = nativeModule.Results_get_query;
    const _native_Results_get_object_type = nativeModule.Results_get_object_type;
    const _native_Results_get_type = nativeModule.Results_get_type;
    class Results {
        constructor(ptr) {
            this[_Results_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof Results))
                throw new TypeError("Expected a Results");
            const out = self[_Results_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed Results");
            return out;
        }
        size() {
            return _native_Results_size(this[_Results_Symbol]);
        }
        indexOf(value) {
            return _native_Results_index_of(this[_Results_Symbol], value);
        }
        indexOfObj(obj) {
            return _native_Results_index_of_obj(this[_Results_Symbol], obj);
        }
        getObj(index) {
            return _native_Results_get_obj(this[_Results_Symbol], index);
        }
        getAny(index) {
            return _native_Results_get_any(this[_Results_Symbol], index);
        }
        getList(index) {
            return _native_Results_get_list(this[_Results_Symbol], index);
        }
        getDictionary(index) {
            return _native_Results_get_dictionary(this[_Results_Symbol], index);
        }
        sortByNames(order) {
            return _native_Results_sort_by_names(this[_Results_Symbol], order);
        }
        snapshot() {
            return _native_Results_snapshot(this[_Results_Symbol]);
        }
        max(column) {
            return _native_Results_max(this[_Results_Symbol], column);
        }
        min(column) {
            return _native_Results_min(this[_Results_Symbol], column);
        }
        average(column) {
            return _native_Results_average(this[_Results_Symbol], column);
        }
        sum(column) {
            return _native_Results_sum(this[_Results_Symbol], column);
        }
        clear() {
            return _native_Results_clear(this[_Results_Symbol]);
        }
        addNotificationCallback(cb, keyPaths) {
            return _native_Results_add_notification_callback(this[_Results_Symbol], cb, keyPaths);
        }
        static fromTable(r, table) {
            return _native_Results_from_table(r, table);
        }
        static fromTableView(r, table) {
            return _native_Results_from_table_view(r, table);
        }
        get isValid() {
            return _native_Results_is_valid(this[_Results_Symbol]);
        }
        get query() {
            return _native_Results_get_query(this[_Results_Symbol]);
        }
        get objectType() {
            return _native_Results_get_object_type(this[_Results_Symbol]);
        }
        get type() {
            return _native_Results_get_type(this[_Results_Symbol]);
        }
    }
    const _Realm_Symbol = Symbol("Realm.Realm.external_pointer");
    const _native_Realm_begin_transaction = nativeModule.Realm_begin_transaction;
    const _native_Realm_commit_transaction = nativeModule.Realm_commit_transaction;
    const _native_Realm_cancel_transaction = nativeModule.Realm_cancel_transaction;
    const _native_Realm_update_schema = nativeModule.Realm_update_schema;
    const _native_Realm_compact = nativeModule.Realm_compact;
    const _native_Realm_convert = nativeModule.Realm_convert;
    const _native_Realm_verify_open = nativeModule.Realm_verify_open;
    const _native_Realm_create_key_path_array = nativeModule.Realm_create_key_path_array;
    const _native_Realm_close = nativeModule.Realm_close;
    const _native_Realm_get_shared_realm = nativeModule.Realm_get_shared_realm;
    const _native_Realm_get_schema_version = nativeModule.Realm_get_schema_version;
    const _native_Realm_config = nativeModule.Realm_config;
    const _native_Realm_schema = nativeModule.Realm_schema;
    const _native_Realm_schema_version = nativeModule.Realm_schema_version;
    const _native_Realm_is_in_transaction = nativeModule.Realm_is_in_transaction;
    const _native_Realm_is_in_migration = nativeModule.Realm_is_in_migration;
    const _native_Realm_is_empty = nativeModule.Realm_is_empty;
    const _native_Realm_is_closed = nativeModule.Realm_is_closed;
    const _native_Realm_DOLLAR_addr = nativeModule.Realm_DOLLAR_addr;
    const _native_Realm_DOLLAR_resetSharedPtr = nativeModule.Realm_DOLLAR_resetSharedPtr;
    class Realm {
        constructor(ptr) {
            this[_Realm_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof Realm))
                throw new TypeError("Expected a Realm");
            const out = self[_Realm_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed Realm");
            return out;
        }
        beginTransaction() {
            return _native_Realm_begin_transaction(this[_Realm_Symbol]);
        }
        commitTransaction() {
            return _native_Realm_commit_transaction(this[_Realm_Symbol]);
        }
        cancelTransaction() {
            return _native_Realm_cancel_transaction(this[_Realm_Symbol]);
        }
        updateSchema(schema, version, migration_function, initialization_function, in_transaction) {
            return _native_Realm_update_schema(this[_Realm_Symbol], schema, version, migration_function, initialization_function, in_transaction);
        }
        compact() {
            return _native_Realm_compact(this[_Realm_Symbol]);
        }
        convert(config) {
            return _native_Realm_convert(this[_Realm_Symbol], config);
        }
        verifyOpen() {
            return _native_Realm_verify_open(this[_Realm_Symbol]);
        }
        createKeyPathArray(table_name, key_paths) {
            return _native_Realm_create_key_path_array(this[_Realm_Symbol], table_name, key_paths);
        }
        close() {
            return _native_Realm_close(this[_Realm_Symbol]);
        }
        static getSharedRealm(config) {
            return _native_Realm_get_shared_realm(config);
        }
        static getSchemaVersion(config) {
            return _native_Realm_get_schema_version(config);
        }
        get config() {
            return _native_Realm_config(this[_Realm_Symbol]);
        }
        get schema() {
            return _native_Realm_schema(this[_Realm_Symbol]);
        }
        get schemaVersion() {
            return _native_Realm_schema_version(this[_Realm_Symbol]);
        }
        get isInTransaction() {
            return _native_Realm_is_in_transaction(this[_Realm_Symbol]);
        }
        get isInMigration() {
            return _native_Realm_is_in_migration(this[_Realm_Symbol]);
        }
        get isEmpty() {
            return _native_Realm_is_empty(this[_Realm_Symbol]);
        }
        get isClosed() {
            return _native_Realm_is_closed(this[_Realm_Symbol]);
        }
        get $addr() {
            return _native_Realm_DOLLAR_addr(this[_Realm_Symbol]);
        }
        $resetSharedPtr() {
            return _native_Realm_DOLLAR_resetSharedPtr(this[_Realm_Symbol]);
        }
    }
    const _RealmCoordinator_Symbol = Symbol("Realm.RealmCoordinator.external_pointer");
    const _native_RealmCoordinator_clear_all_caches = nativeModule.RealmCoordinator_clear_all_caches;
    class RealmCoordinator {
        constructor(ptr) {
            this[_RealmCoordinator_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof RealmCoordinator))
                throw new TypeError("Expected a RealmCoordinator");
            const out = self[_RealmCoordinator_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed RealmCoordinator");
            return out;
        }
        static clearAllCaches() {
            return _native_RealmCoordinator_clear_all_caches();
        }
    }
    const _ObjectNotifier_Symbol = Symbol("Realm.ObjectNotifier.external_pointer");
    const _native_ObjectNotifier_add_callback = nativeModule.ObjectNotifier_add_callback;
    class ObjectNotifier {
        constructor(ptr) {
            this[_ObjectNotifier_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof ObjectNotifier))
                throw new TypeError("Expected a ObjectNotifier");
            const out = self[_ObjectNotifier_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed ObjectNotifier");
            return out;
        }
        addCallback(cb, keyPaths) {
            return _native_ObjectNotifier_add_callback(this[_ObjectNotifier_Symbol], cb, keyPaths);
        }
    }
    const _NotificationToken_Symbol = Symbol("Realm.NotificationToken.external_pointer");
    const _native_NotificationToken_unregister = nativeModule.NotificationToken_unregister;
    const _native_NotificationToken_for_object = nativeModule.NotificationToken_for_object;
    class NotificationToken {
        constructor(ptr) {
            this[_NotificationToken_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof NotificationToken))
                throw new TypeError("Expected a NotificationToken");
            const out = self[_NotificationToken_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed NotificationToken");
            return out;
        }
        unregister() {
            return _native_NotificationToken_unregister(this[_NotificationToken_Symbol]);
        }
        static forObject(notifier, token) {
            return _native_NotificationToken_for_object(notifier, token);
        }
    }
    const _IndexSet_Symbol = Symbol("Realm.IndexSet.external_pointer");
    const _native_IndexSet_Symbol_iterator = nativeModule.IndexSet_Symbol_iterator;
    class IndexSet {
        constructor(ptr) {
            this[_IndexSet_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof IndexSet))
                throw new TypeError("Expected a IndexSet");
            const out = self[_IndexSet_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed IndexSet");
            return out;
        }
        [Symbol.iterator]() {
            return _native_IndexSet_Symbol_iterator(this[_IndexSet_Symbol]);
        }
    }
    const _Collection_Symbol = Symbol("Realm.Collection.external_pointer");
    const _native_Collection_get_any = nativeModule.Collection_get_any;
    const _native_Collection_as_results = nativeModule.Collection_as_results;
    const _native_Collection_snapshot = nativeModule.Collection_snapshot;
    const _native_Collection_get_type = nativeModule.Collection_get_type;
    const _native_Collection_get_object_schema = nativeModule.Collection_get_object_schema;
    const _native_Collection_size = nativeModule.Collection_size;
    const _native_Collection_is_valid = nativeModule.Collection_is_valid;
    class Collection {
        constructor(ptr) {
            this[_Collection_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof Collection))
                throw new TypeError("Expected a Collection");
            const out = self[_Collection_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed Collection");
            return out;
        }
        getAny(ndx) {
            return _native_Collection_get_any(this[_Collection_Symbol], ndx);
        }
        asResults() {
            return _native_Collection_as_results(this[_Collection_Symbol]);
        }
        snapshot() {
            return _native_Collection_snapshot(this[_Collection_Symbol]);
        }
        get type() {
            return _native_Collection_get_type(this[_Collection_Symbol]);
        }
        get objectSchema() {
            return _native_Collection_get_object_schema(this[_Collection_Symbol]);
        }
        get size() {
            return _native_Collection_size(this[_Collection_Symbol]);
        }
        get isValid() {
            return _native_Collection_is_valid(this[_Collection_Symbol]);
        }
    }
    const _native_List_get_obj = nativeModule.List_get_obj;
    const _native_List_get_list = nativeModule.List_get_list;
    const _native_List_get_dictionary = nativeModule.List_get_dictionary;
    const _native_List_move = nativeModule.List_move;
    const _native_List_remove = nativeModule.List_remove;
    const _native_List_remove_all = nativeModule.List_remove_all;
    const _native_List_swap = nativeModule.List_swap;
    const _native_List_delete_all = nativeModule.List_delete_all;
    const _native_List_insert_any = nativeModule.List_insert_any;
    const _native_List_insert_embedded = nativeModule.List_insert_embedded;
    const _native_List_insert_collection = nativeModule.List_insert_collection;
    const _native_List_set_any = nativeModule.List_set_any;
    const _native_List_set_embedded = nativeModule.List_set_embedded;
    const _native_List_set_collection = nativeModule.List_set_collection;
    const _native_List_make = nativeModule.List_make;
    class List extends Collection {
        static _extract(self) {
            if (!(self instanceof List))
                throw new TypeError("Expected a List");
            const out = self[_Collection_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed List");
            return out;
        }
        getObj(ndx) {
            return _native_List_get_obj(this[_Collection_Symbol], ndx);
        }
        getList(path_elem) {
            return _native_List_get_list(this[_Collection_Symbol], path_elem);
        }
        getDictionary(path_elem) {
            return _native_List_get_dictionary(this[_Collection_Symbol], path_elem);
        }
        move(source_ndx, dest_ndx) {
            return _native_List_move(this[_Collection_Symbol], source_ndx, dest_ndx);
        }
        remove(ndx) {
            return _native_List_remove(this[_Collection_Symbol], ndx);
        }
        removeAll() {
            return _native_List_remove_all(this[_Collection_Symbol]);
        }
        swap(ndx1, ndx2) {
            return _native_List_swap(this[_Collection_Symbol], ndx1, ndx2);
        }
        deleteAll() {
            return _native_List_delete_all(this[_Collection_Symbol]);
        }
        insertAny(list_ndx, value) {
            return _native_List_insert_any(this[_Collection_Symbol], list_ndx, value);
        }
        insertEmbedded(ndx) {
            return _native_List_insert_embedded(this[_Collection_Symbol], ndx);
        }
        insertCollection(path_elem, dict_or_list) {
            return _native_List_insert_collection(this[_Collection_Symbol], path_elem, dict_or_list);
        }
        setAny(list_ndx, value) {
            return _native_List_set_any(this[_Collection_Symbol], list_ndx, value);
        }
        setEmbedded(list_ndx) {
            return _native_List_set_embedded(this[_Collection_Symbol], list_ndx);
        }
        setCollection(path_element, dict_or_list) {
            return _native_List_set_collection(this[_Collection_Symbol], path_element, dict_or_list);
        }
        static make(r, parent, col) {
            return _native_List_make(r, parent, col);
        }
    }
    const _native_Set_get_obj = nativeModule.Set_get_obj;
    const _native_Set_insert_any = nativeModule.Set_insert_any;
    const _native_Set_remove_any = nativeModule.Set_remove_any;
    const _native_Set_remove_all = nativeModule.Set_remove_all;
    const _native_Set_delete_all = nativeModule.Set_delete_all;
    const _native_Set_make = nativeModule.Set_make;
    class Set extends Collection {
        static _extract(self) {
            if (!(self instanceof Set))
                throw new TypeError("Expected a Set");
            const out = self[_Collection_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed Set");
            return out;
        }
        getObj(ndx) {
            return _native_Set_get_obj(this[_Collection_Symbol], ndx);
        }
        insertAny(val) {
            return _native_Set_insert_any(this[_Collection_Symbol], val);
        }
        removeAny(val) {
            return _native_Set_remove_any(this[_Collection_Symbol], val);
        }
        removeAll() {
            return _native_Set_remove_all(this[_Collection_Symbol]);
        }
        deleteAll() {
            return _native_Set_delete_all(this[_Collection_Symbol]);
        }
        static make(r, parent, col) {
            return _native_Set_make(r, parent, col);
        }
    }
    const _native_Dictionary_get_list = nativeModule.Dictionary_get_list;
    const _native_Dictionary_get_dictionary = nativeModule.Dictionary_get_dictionary;
    const _native_Dictionary_contains = nativeModule.Dictionary_contains;
    const _native_Dictionary_add_key_based_notification_callback = nativeModule.Dictionary_add_key_based_notification_callback;
    const _native_Dictionary_insert_any = nativeModule.Dictionary_insert_any;
    const _native_Dictionary_insert_embedded = nativeModule.Dictionary_insert_embedded;
    const _native_Dictionary_insert_collection = nativeModule.Dictionary_insert_collection;
    const _native_Dictionary_try_get_any = nativeModule.Dictionary_try_get_any;
    const _native_Dictionary_remove_all = nativeModule.Dictionary_remove_all;
    const _native_Dictionary_try_erase = nativeModule.Dictionary_try_erase;
    const _native_Dictionary_make = nativeModule.Dictionary_make;
    const _native_Dictionary_get_keys = nativeModule.Dictionary_get_keys;
    const _native_Dictionary_get_values = nativeModule.Dictionary_get_values;
    const _native_Dictionary_Symbol_iterator = nativeModule.Dictionary_Symbol_iterator;
    class Dictionary extends Collection {
        static _extract(self) {
            if (!(self instanceof Dictionary))
                throw new TypeError("Expected a Dictionary");
            const out = self[_Collection_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed Dictionary");
            return out;
        }
        getList(path_elem) {
            return _native_Dictionary_get_list(this[_Collection_Symbol], path_elem);
        }
        getDictionary(path_elem) {
            return _native_Dictionary_get_dictionary(this[_Collection_Symbol], path_elem);
        }
        contains(key) {
            return _native_Dictionary_contains(this[_Collection_Symbol], key);
        }
        addKeyBasedNotificationCallback(cb, keyPaths) {
            return _native_Dictionary_add_key_based_notification_callback(this[_Collection_Symbol], cb, keyPaths);
        }
        insertAny(key, value) {
            return _native_Dictionary_insert_any(this[_Collection_Symbol], key, value);
        }
        insertEmbedded(key) {
            return _native_Dictionary_insert_embedded(this[_Collection_Symbol], key);
        }
        insertCollection(path_elem, dict_or_list) {
            return _native_Dictionary_insert_collection(this[_Collection_Symbol], path_elem, dict_or_list);
        }
        tryGetAny(key) {
            return _native_Dictionary_try_get_any(this[_Collection_Symbol], key);
        }
        removeAll() {
            return _native_Dictionary_remove_all(this[_Collection_Symbol]);
        }
        tryErase(key) {
            return _native_Dictionary_try_erase(this[_Collection_Symbol], key);
        }
        static make(r, parent, col) {
            return _native_Dictionary_make(r, parent, col);
        }
        get keys() {
            return _native_Dictionary_get_keys(this[_Collection_Symbol]);
        }
        get values() {
            return _native_Dictionary_get_values(this[_Collection_Symbol]);
        }
        [Symbol.iterator]() {
            return _native_Dictionary_Symbol_iterator(this[_Collection_Symbol]);
        }
    }
    const _LoggerFactory_Symbol = Symbol("Realm.LoggerFactory.external_pointer");
    class LoggerFactory {
        constructor(ptr) {
            this[_LoggerFactory_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof LoggerFactory))
                throw new TypeError("Expected a LoggerFactory");
            const out = self[_LoggerFactory_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed LoggerFactory");
            return out;
        }
    }
    const _ThreadSafeReference_Symbol = Symbol("Realm.ThreadSafeReference.external_pointer");
    class ThreadSafeReference {
        constructor(ptr) {
            this[_ThreadSafeReference_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof ThreadSafeReference))
                throw new TypeError("Expected a ThreadSafeReference");
            const out = self[_ThreadSafeReference_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed ThreadSafeReference");
            return out;
        }
    }
    const _Scheduler_Symbol = Symbol("Realm.Scheduler.external_pointer");
    class Scheduler {
        constructor(ptr) {
            this[_Scheduler_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof Scheduler))
                throw new TypeError("Expected a Scheduler");
            const out = self[_Scheduler_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed Scheduler");
            return out;
        }
    }
    const _JsPlatformHelpers_Symbol = Symbol("Realm.JsPlatformHelpers.external_pointer");
    const _native_JsPlatformHelpers_set_default_realm_file_directory = nativeModule.JsPlatformHelpers_set_default_realm_file_directory;
    const _native_JsPlatformHelpers_default_realm_file_directory = nativeModule.JsPlatformHelpers_default_realm_file_directory;
    const _native_JsPlatformHelpers_ensure_directory_exists_for_file = nativeModule.JsPlatformHelpers_ensure_directory_exists_for_file;
    const _native_JsPlatformHelpers_copy_bundled_realm_files = nativeModule.JsPlatformHelpers_copy_bundled_realm_files;
    const _native_JsPlatformHelpers_remove_realm_files_from_directory = nativeModule.JsPlatformHelpers_remove_realm_files_from_directory;
    const _native_JsPlatformHelpers_remove_file = nativeModule.JsPlatformHelpers_remove_file;
    const _native_JsPlatformHelpers_remove_directory = nativeModule.JsPlatformHelpers_remove_directory;
    const _native_JsPlatformHelpers_exclude_from_icloud_backup = nativeModule.JsPlatformHelpers_exclude_from_icloud_backup;
    const _native_JsPlatformHelpers_get_cpu_arch = nativeModule.JsPlatformHelpers_get_cpu_arch;
    class JsPlatformHelpers {
        constructor(ptr) {
            this[_JsPlatformHelpers_Symbol] = ptr;
        }
        static _extract(self) {
            if (!(self instanceof JsPlatformHelpers))
                throw new TypeError("Expected a JsPlatformHelpers");
            const out = self[_JsPlatformHelpers_Symbol];
            if (!out)
                throw new TypeError("Received an improperly constructed JsPlatformHelpers");
            return out;
        }
        static setDefaultRealmFileDirectory(dir) {
            return _native_JsPlatformHelpers_set_default_realm_file_directory(dir);
        }
        static defaultRealmFileDirectory() {
            return _native_JsPlatformHelpers_default_realm_file_directory();
        }
        static ensureDirectoryExistsForFile(file) {
            return _native_JsPlatformHelpers_ensure_directory_exists_for_file(file);
        }
        static copyBundledRealmFiles() {
            return _native_JsPlatformHelpers_copy_bundled_realm_files();
        }
        static removeRealmFilesFromDirectory(directory) {
            return _native_JsPlatformHelpers_remove_realm_files_from_directory(directory);
        }
        static removeFile(path) {
            return _native_JsPlatformHelpers_remove_file(path);
        }
        static removeDirectory(path) {
            return _native_JsPlatformHelpers_remove_directory(path);
        }
        static excludeFromIcloudBackup(path, value) {
            return _native_JsPlatformHelpers_exclude_from_icloud_backup(path, value);
        }
        static getCpuArch() {
            return _native_JsPlatformHelpers_get_cpu_arch();
        }
    }
    Object.defineProperties(binding, {
        Helpers: { value: Helpers, writable: false, configurable: false },
        LogCategoryRef: { value: LogCategoryRef, writable: false, configurable: false },
        Logger: { value: Logger, writable: false, configurable: false },
        ConstTableRef: { value: ConstTableRef, writable: false, configurable: false },
        TableRef: { value: TableRef, writable: false, configurable: false },
        Obj: { value: Obj, writable: false, configurable: false },
        Transaction: { value: Transaction, writable: false, configurable: false },
        ObjectStore: { value: ObjectStore, writable: false, configurable: false },
        Timestamp: { value: Timestamp, writable: false, configurable: false },
        Geospatial: { value: Geospatial, writable: false, configurable: false },
        ObjLink: { value: ObjLink, writable: false, configurable: false },
        KeyPathMapping: { value: KeyPathMapping, writable: false, configurable: false },
        Query: { value: Query, writable: false, configurable: false },
        SortDescriptor: { value: SortDescriptor, writable: false, configurable: false },
        TableView: { value: TableView, writable: false, configurable: false },
        Results: { value: Results, writable: false, configurable: false },
        Realm: { value: Realm, writable: false, configurable: false },
        RealmCoordinator: { value: RealmCoordinator, writable: false, configurable: false },
        ObjectNotifier: { value: ObjectNotifier, writable: false, configurable: false },
        NotificationToken: { value: NotificationToken, writable: false, configurable: false },
        IndexSet: { value: IndexSet, writable: false, configurable: false },
        Collection: { value: Collection, writable: false, configurable: false },
        List: { value: List, writable: false, configurable: false },
        Set: { value: Set, writable: false, configurable: false },
        Dictionary: { value: Dictionary, writable: false, configurable: false },
        LoggerFactory: { value: LoggerFactory, writable: false, configurable: false },
        ThreadSafeReference: { value: ThreadSafeReference, writable: false, configurable: false },
        Scheduler: { value: Scheduler, writable: false, configurable: false },
        JsPlatformHelpers: { value: JsPlatformHelpers, writable: false, configurable: false },
    });
    nativeModule.injectInjectables({
        Long: bson_1.Long,
        ArrayBuffer,
        Float: utils.Float,
        Status: utils.Status,
        ObjectId: bson_1.ObjectId,
        UUID: bson_1.UUID,
        Decimal128: bson_1.Decimal128,
        EJSON_parse: bson_1.EJSON.parse,
        EJSON_stringify: bson_1.EJSON.stringify,
        Symbol_for: Symbol.for,
        Helpers,
        LogCategoryRef,
        Logger,
        ConstTableRef,
        TableRef,
        Obj,
        Transaction,
        ObjectStore,
        Timestamp,
        Geospatial,
        ObjLink,
        KeyPathMapping,
        Query,
        SortDescriptor,
        TableView,
        Results,
        Realm,
        RealmCoordinator,
        ObjectNotifier,
        NotificationToken,
        IndexSet,
        Collection,
        List,
        Set,
        Dictionary,
        LoggerFactory,
        ThreadSafeReference,
        Scheduler,
        JsPlatformHelpers,
    });
    (0, patch_1.applyPatch)(binding);
    exports.isReady = true;
    resolveReadyPromise();
}
exports.injectNativeModule = injectNativeModule;
//# sourceMappingURL=wrapper.generated.js.map