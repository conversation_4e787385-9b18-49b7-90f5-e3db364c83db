import React, { useState } from 'react';
import { View, StyleSheet, SafeAreaView, TouchableOpacity, Text, Alert, ActivityIndicator } from 'react-native';
import { WebView } from 'react-native-webview';
import { LinearGradient } from 'expo-linear-gradient';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Colors, Gradients } from '../constants/Colors';
import { RootStackParamList } from '../types/navigation';

type BuyScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Buy'>;

export const BuyScreen: React.FC = () => {
  const navigation = useNavigation<BuyScreenNavigationProp>();
  const [loading, setLoading] = useState(true);
  const [canGoBack, setCanGoBack] = useState(false);
  const [webViewRef, setWebViewRef] = useState<WebView | null>(null);

  const handleBackPress = () => {
    if (canGoBack && webViewRef) {
      webViewRef.goBack();
    } else {
      navigation.goBack();
    }
  };

  const handleWebViewNavigationStateChange = (navState: any) => {
    setCanGoBack(navState.canGoBack);
  };

  const handleWebViewError = () => {
    Alert.alert(
      'Connection Error',
      'Unable to load the store. Please check your internet connection and try again.',
      [
        { text: 'Retry', onPress: () => webViewRef?.reload() },
        { text: 'Go Back', onPress: () => navigation.goBack() }
      ]
    );
  };

  const handleLoadEnd = () => {
    setLoading(false);
  };

  return (
    <LinearGradient colors={Gradients.background} style={styles.container}>
      <StatusBar style="light" />
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={handleBackPress}
            activeOpacity={0.7}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>OhkoGames Store</Text>
          <View style={styles.headerSpacer} />
        </View>

        {/* WebView Container */}
        <View style={styles.webViewContainer}>
          {loading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.white} />
              <Text style={styles.loadingText}>Loading store...</Text>
            </View>
          )}
          
          <WebView
            ref={(ref) => setWebViewRef(ref)}
            source={{ uri: 'https://ohkogames.com' }}
            style={styles.webView}
            onNavigationStateChange={handleWebViewNavigationStateChange}
            onError={handleWebViewError}
            onLoadEnd={handleLoadEnd}
            startInLoadingState={true}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            allowsBackForwardNavigationGestures={true}
            scalesPageToFit={true}
            bounces={false}
            scrollEnabled={true}
            // Shopify-specific settings
            userAgent="Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
            // Allow secure connections
            mixedContentMode="compatibility"
            // Handle external links
            onShouldStartLoadWithRequest={(request) => {
              // Allow navigation within ohkogames.com and common payment providers
              const allowedDomains = [
                'ohkogames.com',
                'shopify.com',
                'shopifycs.com',
                'paypal.com',
                'stripe.com',
                'checkout.com'
              ];
              
              const url = request.url.toLowerCase();
              const isAllowed = allowedDomains.some(domain => url.includes(domain));
              
              if (!isAllowed) {
                Alert.alert(
                  'External Link',
                  'This link will open outside the app. Continue?',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    { text: 'Continue', onPress: () => {
                      // In a real app, you might want to open this in the system browser
                      console.log('External link:', request.url);
                    }}
                  ]
                );
                return false;
              }
              
              return true;
            }}
          />
        </View>

        {/* Bottom Navigation Hint */}
        <View style={styles.bottomHint}>
          <Text style={styles.hintText}>
            {canGoBack ? 'Tap back to navigate within store' : 'Tap back to return to home'}
          </Text>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    color: Colors.white,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40, // Same width as back button to center title
  },
  webViewContainer: {
    flex: 1,
    backgroundColor: Colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
    marginTop: 10,
  },
  webView: {
    flex: 1,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.white,
    zIndex: 1,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.primary,
    fontWeight: '500',
  },
  bottomHint: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  hintText: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
});
