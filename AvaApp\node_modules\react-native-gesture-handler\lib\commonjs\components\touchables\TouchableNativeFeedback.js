"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _reactNative = require("react-native");

/**
 * @deprecated TouchableNativeFeedback will be removed in the future version of Gesture Handler. Use Pressable instead.
 */
const TouchableNativeFeedback = _reactNative.TouchableNativeFeedback;
var _default = TouchableNativeFeedback;
exports.default = _default;
//# sourceMappingURL=TouchableNativeFeedback.js.map