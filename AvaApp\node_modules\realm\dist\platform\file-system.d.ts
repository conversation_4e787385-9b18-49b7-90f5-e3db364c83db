type FileSystemType = {
    isAbsolutePath(path: string): boolean;
    joinPaths(...segments: string[]): string;
    removeFile(path: string): void;
    removeDirectory(path: string): void;
    ensureDirectoryForFile(path: string): void;
    setDefaultDirectoryPath(path: string): void;
    getDefaultDirectoryPath(): string;
    exists(path: string): boolean;
    copyBundledRealmFiles(): void;
    removeRealmFilesFromDirectory(path: string): void;
};
export type Dirent = {
    name: string;
    isFile(): boolean;
    isDirectory(): boolean;
};
export declare const fs: FileSystemType;
export declare function inject(value: FileSystemType): void;
export {};
