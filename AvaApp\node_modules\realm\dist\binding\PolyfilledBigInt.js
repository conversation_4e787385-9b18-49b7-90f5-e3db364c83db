"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.PolyfilledBigInt = void 0;
const bson_1 = require("bson");
const assert_1 = require("../assert");
class PolyfilledBigInt {
    static add(a, b) {
        assert_1.assert.instanceOf(a, bson_1.Long);
        assert_1.assert.instanceOf(b, bson_1.Long);
        return a.add(b);
    }
    static equals(a, b) {
        assert_1.assert.instanceOf(a, bson_1.Long);
        (0, assert_1.assert)(typeof b === "number" || typeof b === "string" || (typeof b === "object" && b instanceof bson_1.Long), "Expected a 'BSON.Long', or number, or string.");
        return a.equals(b);
    }
    static isInt(a) {
        return a instanceof bson_1.Long;
    }
    static numToInt(a) {
        return bson_1.Long.fromNumber(a);
    }
    static strToInt(a) {
        return bson_1.Long.fromString(a);
    }
    static intToNum(a) {
        assert_1.assert.instanceOf(a, bson_1.Long);
        return a.toNumber();
    }
}
exports.PolyfilledBigInt = PolyfilledBigInt;
//# sourceMappingURL=PolyfilledBigInt.js.map