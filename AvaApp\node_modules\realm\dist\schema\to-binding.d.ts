import { binding } from "../binding";
import type { CanonicalObjectSchema, CanonicalPropertySchema, PropertyTypeName } from "../schema";
type BindingObjectSchema = binding.ObjectSchema_Relaxed;
type BindingProperty = binding.Property_Relaxed;
import BindingPropertyType = binding.PropertyType;
/** @internal */
export declare const TYPE_MAPPINGS: Record<PropertyTypeName, BindingPropertyType>;
/** @internal */
export declare function toBindingSchema(schema: CanonicalObjectSchema[]): BindingObjectSchema[];
/** @internal */
export declare function toBindingObjectSchema(schema: CanonicalObjectSchema): BindingObjectSchema;
/** @internal */
export declare function toBindingPropertySchema(name: string, schema: CanonicalPropertySchema): BindingProperty;
/** @internal */
export declare function toBindingPropertyType(schema: CanonicalPropertySchema): BindingPropertyType;
export {};
