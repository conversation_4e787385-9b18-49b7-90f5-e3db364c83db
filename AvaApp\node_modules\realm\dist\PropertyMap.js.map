{"version": 3, "file": "PropertyMap.js", "sourceRoot": "", "sources": ["../src/PropertyMap.ts"], "names": [], "mappings": ";AAAA,4EAA4E;AAC5E,EAAE;AACF,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,6CAA6C;AAC7C,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,EAAE;AACF,4EAA4E;;;AAG5E,qCAAkC;AAElC,uDAA0D;AAG1D,MAAM,6BAA8B,SAAQ,KAAK;IAC/C;QACE,KAAK,CAAC,qDAAqD,CAAC,CAAC;IAC/D,CAAC;CACF;AAED,gBAAgB;AAChB,MAAa,WAAW;IACd,gBAAgB,GAAkB,IAAI,CAAC;IACvC,WAAW,GAAG,KAAK,CAAC;IACpB,OAAO,GAAgD,EAAE,CAAC;IAClE;;OAEG;IACK,qBAAqB,GAAwB,IAAI,GAAG,EAAE,CAAC;IACvD,MAAM,GAAa,EAAE,CAAC;IAEvB,UAAU,CACf,YAAkC,EAClC,qBAA4C,EAC5C,QAAiC,EACjC,OAAsB;QAEtB,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,GAAG,YAAY,CAAC;QACzF,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,MAAM,UAAU,GAAG,CAAC,GAAG,mBAAmB,EAAE,GAAG,kBAAkB,CAAC,CAAC;QACnE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,WAAW,CAC/B,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC1B,MAAM,YAAY,GAAG,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,IAAI,CAAC;YAC1D,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU;gBAClC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,SAAS,uCAA+B;gBACpG,CAAC,CAAC,KAAK,CAAC;YAEV,MAAM,uBAAuB,GAAG,qBAAqB,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAC/E,IAAA,eAAM,EAAC,uBAAuB,EAAE,aAAa,YAAY,0CAA0C,CAAC,CAAC;YACrG,MAAM,OAAO,GAAG,IAAA,uCAAqB,EACnC,EAAE,GAAG,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,EAAE,YAAY,EAAE,uBAAuB,CAAC,YAAY,EAAE,EAC/F,OAAO,CACR,CAAC;YACF,0DAA0D;YAC1D,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;YAC5C,OAAO,CAAC,OAAO,GAAG,OAAO,YAAY,KAAK,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;YAEvF,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QACjC,CAAC,CAAC,CACH,CAAC;QACF,IAAI,CAAC,qBAAqB,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9G,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QAC5D,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC1B,CAAC;IAEM,GAAG,GAAG,CAAC,QAAgB,EAAmB,EAAE;QACjD,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACvC,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,aAAa,QAAQ,wBAAwB,IAAI,CAAC,gBAAgB,WAAW,CAAC,CAAC;aAChG;YACD,OAAO,OAAO,CAAC;SAChB;aAAM;YACL,MAAM,IAAI,6BAA6B,EAAE,CAAC;SAC3C;IACH,CAAC,CAAC;IAEK,OAAO,GAAG,CAAI,SAAyB,EAAW,EAAE;QACzD,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAY,CAAC;SACxE;aAAM;YACL,MAAM,IAAI,6BAA6B,EAAE,CAAC;SAC3C;IACH,CAAC,CAAC;IAEF,IAAW,KAAK;QACd,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,IAAI,CAAC,MAAM,CAAC;SACpB;aAAM;YACL,MAAM,IAAI,6BAA6B,EAAE,CAAC;SAC3C;IACH,CAAC;CACF;AAvED,kCAuEC"}