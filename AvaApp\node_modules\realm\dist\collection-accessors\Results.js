"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.createResultsAccessor = void 0;
const binding_1 = require("../binding");
const indirect_1 = require("../indirect");
const Dictionary_1 = require("./Dictionary");
const List_1 = require("./List");
const OrderedCollection_1 = require("./OrderedCollection");
/** @internal */
function createResultsAccessor(options) {
    return options.itemType === 9 /* binding.PropertyType.Mixed */
        ? createResultsAccessorForMixed(options)
        : createResultsAccessorForKnownType(options);
}
exports.createResultsAccessor = createResultsAccessor;
function createResultsAccessorForMixed({ realm, typeHelpers, }) {
    return {
        get(results, index) {
            const value = results.getAny(index);
            switch (value) {
                case binding_1.binding.ListSentinel: {
                    const accessor = (0, List_1.createListAccessor)({ realm, typeHelpers, itemType: 9 /* binding.PropertyType.Mixed */ });
                    return new indirect_1.indirect.List(realm, results.getList(index), accessor, typeHelpers);
                }
                case binding_1.binding.DictionarySentinel: {
                    const accessor = (0, Dictionary_1.createDictionaryAccessor)({ realm, typeHelpers, itemType: 9 /* binding.PropertyType.Mixed */ });
                    return new indirect_1.indirect.Dictionary(realm, results.getDictionary(index), accessor, typeHelpers);
                }
                default:
                    return typeHelpers.fromBinding(value);
            }
        },
    };
}
function createResultsAccessorForKnownType({ typeHelpers, itemType, }) {
    return {
        get: (0, OrderedCollection_1.createDefaultGetter)({ fromBinding: typeHelpers.fromBinding, itemType }),
    };
}
//# sourceMappingURL=Results.js.map