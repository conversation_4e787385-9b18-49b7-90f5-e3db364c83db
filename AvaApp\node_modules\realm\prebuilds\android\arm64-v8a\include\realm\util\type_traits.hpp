/*************************************************************************
 *
 * Copyright 2016 Realm Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 **************************************************************************/

#ifndef REALM_UTIL_TYPE_TRAITS_HPP
#define REALM_UTIL_TYPE_TRAITS_HPP

namespace realm::util {
template <typename T>
struct TypeIdentity {
    using type = T;
};

template <typename T>
using type_identity_t = typename TypeIdentity<T>::type;

} // namespace realm::util

#endif // REALM_UTIL_TYPE_TRAITS_HPP
