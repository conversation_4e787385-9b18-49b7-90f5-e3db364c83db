"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.createObjectTypeHelpers = void 0;
const binding_1 = require("../binding");
const assert_1 = require("../assert");
const Object_1 = require("../Object");
const symbols_1 = require("../symbols");
const null_passthrough_1 = require("./null-passthrough");
/** @internal */
function createObjectTypeHelpers({ realm, name, objectType, optional, getClassHelpers, }) {
    (0, assert_1.assert)(objectType);
    const helpers = getClassHelpers(objectType);
    const { wrapObject } = helpers;
    return {
        toBinding: (0, null_passthrough_1.nullPassthrough)((value, options) => {
            if (value instanceof Object_1.RealmObject &&
                value.constructor.name === objectType &&
                value[symbols_1.OBJECT_REALM].internal.$addr === realm.internal.$addr) {
                return value[symbols_1.OBJECT_INTERNAL];
            }
            else {
                // TODO: Consider exposing a way for calling code to disable object creation
                assert_1.assert.object(value, name);
                // Use the update mode if set; otherwise, the object is assumed to be an
                // unmanaged object that the user wants to create.
                // TODO: Ideally use `options?.updateMode` instead of `realm.currentUpdateMode`.
                const createdObject = Object_1.RealmObject.create(realm, value, realm.currentUpdateMode ?? Object_1.UpdateMode.Never, {
                    helpers,
                    createObj: options?.createObj,
                });
                return createdObject[symbols_1.OBJECT_INTERNAL];
            }
        }, optional),
        fromBinding: (0, null_passthrough_1.nullPassthrough)((value) => {
            if (value instanceof binding_1.binding.ObjLink) {
                const table = binding_1.binding.Helpers.getTable(realm.internal, value.tableKey);
                const linkedObj = table.getObject(value.objKey);
                return wrapObject(linkedObj);
            }
            else {
                assert_1.assert.instanceOf(value, binding_1.binding.Obj);
                return wrapObject(value);
            }
        }, optional),
    };
}
exports.createObjectTypeHelpers = createObjectTypeHelpers;
//# sourceMappingURL=Object.js.map