import type { Realm } from "./Realm";
import type { CanonicalObjectSchema } from "./schema";
export declare enum RealmEvent {
    Change = "change",
    Schema = "schema",
    BeforeNotify = "beforenotify"
}
export type RealmListenerCallback = (
/**
 * The Realm in which the change event occurred.
 */
realm: Realm, 
/**
 * The name of the event that occurred.
 */
name: RealmEvent, 
/**
 * The schema of the Realm file when the event occurred.
 */
schema?: CanonicalObjectSchema[]) => void;
export declare class RealmListeners {
    private realm;
    private eventType;
    /**
     * Keeps tracked of registered listener callbacks for Realm class notifications.
     */
    constructor(realm: Realm, eventType: RealmEvent);
    private listeners;
    notify(schema?: CanonicalObjectSchema[]): void;
    add(callback: RealmListenerCallback): void;
    remove(callback: RealmListenerCallback): void;
    removeAll(): void;
}
