import { binding } from "../binding";
import type { Dictionary } from "../Dictionary";
import type { TypeHelpers } from "../TypeHelpers";
import type { Realm } from "../Realm";
/** @internal */
export type DictionaryAccessor<T = unknown> = {
    get: (dictionary: binding.Dictionary, key: string) => T;
    set: (dictionary: binding.Dictionary, key: string, value: T) => void;
};
type DictionaryAccessorFactoryOptions<T> = {
    realm: Realm;
    typeHelpers: TypeHelpers<T>;
    itemType: binding.PropertyType;
    isEmbedded?: boolean;
};
/** @internal */
export declare function createDictionaryAccessor<T>(options: DictionaryAccessorFactoryOptions<T>): DictionaryAccessor<T>;
/** @internal */
export declare function insertIntoDictionaryOfMixed(dictionary: Dictionary | Record<string, unknown>, internal: binding.Dictionary, toBinding: TypeHelpers["toBinding"]): void;
/** @internal */
export declare function isJsOrRealmDictionary(value: unknown): value is Dictionary | Record<string, unknown>;
/** @internal */
export declare function isPOJO(value: unknown): value is Record<string, unknown>;
export {};
