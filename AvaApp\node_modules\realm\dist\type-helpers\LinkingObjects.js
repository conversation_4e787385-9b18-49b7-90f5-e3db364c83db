"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.createLinkingObjectsTypeHelpers = void 0;
const binding_1 = require("../binding");
const assert_1 = require("../assert");
const default_1 = require("./default");
/** @internal */
function createLinkingObjectsTypeHelpers({ objectType, getClassHelpers }) {
    (0, assert_1.assert)(objectType);
    const { wrapObject } = getClassHelpers(objectType);
    return {
        toBinding: default_1.defaultToBinding,
        fromBinding(value) {
            assert_1.assert.instanceOf(value, binding_1.binding.Obj);
            return wrapObject(value);
        },
    };
}
exports.createLinkingObjectsTypeHelpers = createLinkingObjectsTypeHelpers;
//# sourceMappingURL=LinkingObjects.js.map