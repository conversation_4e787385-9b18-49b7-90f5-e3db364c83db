import * as SQLite from 'expo-sqlite';

// Database interface types
export interface User {
  id: number;
  name: string;
  avatar?: string;
  created_at: string;
}

export interface Transaction {
  id: number;
  user_id: number;
  type: 'buy' | 'sell' | 'trade';
  item_name: string;
  amount: number;
  price?: number;
  created_at: string;
}

export interface CollectionItem {
  id: number;
  user_id: number;
  name: string;
  description?: string;
  image_url?: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  acquired_at: string;
}

export interface CatalogItem {
  id: number;
  mongo_id: string; // Original MongoDB _id
  name: string;
  description?: string;
  image_url?: string;
  price?: number;
  category?: string;
  rarity?: string;
  set_name?: string;
  card_number?: string;
  artist?: string;
  type?: string;
  mana_cost?: string;
  power?: string;
  toughness?: string;
  text?: string;
  flavor_text?: string;
  created_at: string;
  updated_at: string;
}

// Database class
class Database {
  private db: SQLite.SQLiteDatabase | null = null;

  async init(): Promise<void> {
    try {
      this.db = await SQLite.openDatabaseAsync('ava_app.db');
      await this.createTables();
      await this.seedInitialData();
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Users table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        avatar TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Transactions table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('buy', 'sell', 'trade')),
        item_name TEXT NOT NULL,
        amount INTEGER NOT NULL,
        price REAL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      );
    `);

    // Collection items table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS collection_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        image_url TEXT,
        rarity TEXT NOT NULL CHECK (rarity IN ('common', 'rare', 'epic', 'legendary')),
        acquired_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      );
    `);

    // Catalog items table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS catalog_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        mongo_id TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        image_url TEXT,
        price REAL,
        category TEXT,
        rarity TEXT,
        set_name TEXT,
        card_number TEXT,
        artist TEXT,
        type TEXT,
        mana_cost TEXT,
        power TEXT,
        toughness TEXT,
        text TEXT,
        flavor_text TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create indexes for better search performance
    await this.db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_catalog_name ON catalog_items(name);
    `);
    await this.db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_catalog_category ON catalog_items(category);
    `);
    await this.db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_catalog_set ON catalog_items(set_name);
    `);
  }

  private async seedInitialData(): Promise<void> {
    if (!this.db) return;

    // Check if user already exists
    const existingUser = await this.db.getFirstAsync('SELECT * FROM users WHERE name = ?', ['Ava']);
    
    if (!existingUser) {
      // Create default user (Ava)
      await this.db.runAsync(
        'INSERT INTO users (name) VALUES (?)',
        ['Ava']
      );

      // Add some sample collection items
      const userId = 1; // Ava's user ID
      const sampleItems = [
        { name: 'Digital Card #001', rarity: 'common', description: 'A basic digital trading card' },
        { name: 'Rare Crystal', rarity: 'rare', description: 'A beautiful rare crystal' },
        { name: 'Epic Sword', rarity: 'epic', description: 'A legendary weapon' },
      ];

      for (const item of sampleItems) {
        await this.db.runAsync(
          'INSERT INTO collection_items (user_id, name, description, rarity) VALUES (?, ?, ?, ?)',
          [userId, item.name, item.description, item.rarity]
        );
      }
    }
  }

  // User methods
  async getUser(id: number): Promise<User | null> {
    if (!this.db) return null;
    return await this.db.getFirstAsync('SELECT * FROM users WHERE id = ?', [id]) as User | null;
  }

  async getUserByName(name: string): Promise<User | null> {
    if (!this.db) return null;
    return await this.db.getFirstAsync('SELECT * FROM users WHERE name = ?', [name]) as User | null;
  }

  // Transaction methods
  async addTransaction(transaction: Omit<Transaction, 'id' | 'created_at'>): Promise<void> {
    if (!this.db) return;
    await this.db.runAsync(
      'INSERT INTO transactions (user_id, type, item_name, amount, price) VALUES (?, ?, ?, ?, ?)',
      [transaction.user_id, transaction.type, transaction.item_name, transaction.amount, transaction.price || null]
    );
  }

  async getTransactions(userId: number): Promise<Transaction[]> {
    if (!this.db) return [];
    return await this.db.getAllAsync('SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC', [userId]) as Transaction[];
  }

  // Collection methods
  async getCollectionItems(userId: number): Promise<CollectionItem[]> {
    if (!this.db) return [];
    return await this.db.getAllAsync('SELECT * FROM collection_items WHERE user_id = ? ORDER BY acquired_at DESC', [userId]) as CollectionItem[];
  }

  async addCollectionItem(item: Omit<CollectionItem, 'id' | 'acquired_at'>): Promise<void> {
    if (!this.db) return;
    await this.db.runAsync(
      'INSERT INTO collection_items (user_id, name, description, image_url, rarity) VALUES (?, ?, ?, ?, ?)',
      [item.user_id, item.name, item.description || null, item.image_url || null, item.rarity]
    );
  }

  // Catalog methods
  async syncCatalogFromMongoDB(): Promise<number> {
    if (!this.db) return 0;

    try {
      console.log('Starting catalog sync from MongoDB...');

      // Note: In a real React Native app, you'd need to use a different approach
      // since MongoDB driver doesn't work directly in React Native.
      // You would typically:
      // 1. Create an API endpoint on your server that fetches from MongoDB
      // 2. Call that API from the React Native app
      // 3. Insert the data into SQLite

      // For now, let's create a mock sync that would work with an API
      const catalogData = await this.fetchCatalogFromAPI();

      let syncedCount = 0;
      for (const item of catalogData) {
        await this.upsertCatalogItem(item);
        syncedCount++;
      }

      console.log(`Synced ${syncedCount} catalog items`);
      return syncedCount;
    } catch (error) {
      console.error('Catalog sync failed:', error);
      throw error;
    }
  }

  private async fetchCatalogFromAPI(): Promise<any[]> {
    // This would be replaced with actual API call
    // For now, return mock data that represents MongoDB structure
    return [
      {
        _id: '507f1f77bcf86cd799439011',
        name: 'Lightning Bolt',
        description: 'Lightning Bolt deals 3 damage to any target.',
        image_url: 'https://example.com/lightning-bolt.jpg',
        price: 2.50,
        category: 'Instant',
        rarity: 'common',
        set_name: 'Alpha',
        card_number: '161',
        artist: 'Christopher Rush',
        type: 'Instant',
        mana_cost: '{R}',
        text: 'Lightning Bolt deals 3 damage to any target.',
      },
      {
        _id: '507f1f77bcf86cd799439012',
        name: 'Black Lotus',
        description: 'Add three mana of any one color.',
        image_url: 'https://example.com/black-lotus.jpg',
        price: 25000.00,
        category: 'Artifact',
        rarity: 'legendary',
        set_name: 'Alpha',
        card_number: '232',
        artist: 'Christopher Rush',
        type: 'Artifact',
        mana_cost: '{0}',
        text: '{T}, Sacrifice Black Lotus: Add three mana of any one color.',
      }
    ];
  }

  private async upsertCatalogItem(mongoItem: any): Promise<void> {
    if (!this.db) return;

    await this.db.runAsync(`
      INSERT OR REPLACE INTO catalog_items (
        mongo_id, name, description, image_url, price, category, rarity,
        set_name, card_number, artist, type, mana_cost, text, flavor_text,
        updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `, [
      mongoItem._id,
      mongoItem.name,
      mongoItem.description || null,
      mongoItem.image_url || null,
      mongoItem.price || null,
      mongoItem.category || null,
      mongoItem.rarity || null,
      mongoItem.set_name || null,
      mongoItem.card_number || null,
      mongoItem.artist || null,
      mongoItem.type || null,
      mongoItem.mana_cost || null,
      mongoItem.text || null,
      mongoItem.flavor_text || null,
    ]);
  }

  async searchCatalog(query: string, limit: number = 50): Promise<CatalogItem[]> {
    if (!this.db) return [];

    const searchQuery = `%${query}%`;
    return await this.db.getAllAsync(`
      SELECT * FROM catalog_items
      WHERE name LIKE ? OR description LIKE ? OR text LIKE ?
      ORDER BY name
      LIMIT ?
    `, [searchQuery, searchQuery, searchQuery, limit]) as CatalogItem[];
  }

  async getCatalogByCategory(category: string): Promise<CatalogItem[]> {
    if (!this.db) return [];
    return await this.db.getAllAsync('SELECT * FROM catalog_items WHERE category = ? ORDER BY name', [category]) as CatalogItem[];
  }

  async getCatalogBySet(setName: string): Promise<CatalogItem[]> {
    if (!this.db) return [];
    return await this.db.getAllAsync('SELECT * FROM catalog_items WHERE set_name = ? ORDER BY card_number', [setName]) as CatalogItem[];
  }

  async getAllCatalogItems(limit: number = 100): Promise<CatalogItem[]> {
    if (!this.db) return [];
    return await this.db.getAllAsync('SELECT * FROM catalog_items ORDER BY name LIMIT ?', [limit]) as CatalogItem[];
  }

  async getCatalogItemCount(): Promise<number> {
    if (!this.db) return 0;
    const result = await this.db.getFirstAsync('SELECT COUNT(*) as count FROM catalog_items') as { count: number };
    return result.count;
  }
}

// Export singleton instance
export const database = new Database();
