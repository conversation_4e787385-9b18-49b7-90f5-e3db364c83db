import * as SQLite from 'expo-sqlite';

// Database interface types
export interface User {
  id: number;
  name: string;
  avatar?: string;
  created_at: string;
}

export interface Transaction {
  id: number;
  user_id: number;
  type: 'buy' | 'sell' | 'trade';
  item_name: string;
  amount: number;
  price?: number;
  created_at: string;
}

export interface CollectionItem {
  id: number;
  user_id: number;
  name: string;
  description?: string;
  image_url?: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  acquired_at: string;
}

export interface CatalogItem {
  id: number;
  mongo_id: string; // Original MongoDB _id
  name: string;
  description?: string;
  image_url?: string;
  price?: number;
  category?: string;
  rarity?: string;
  set_name?: string;
  card_number?: string;
  artist?: string;
  type?: string;
  mana_cost?: string;
  power?: string;
  toughness?: string;
  text?: string;
  flavor_text?: string;
  raw_data?: string; // Original MongoDB document as JSON
  created_at: string;
  updated_at: string;
}

// Database class
class Database {
  private db: SQLite.SQLiteDatabase | null = null;

  async init(): Promise<void> {
    try {
      this.db = await SQLite.openDatabaseAsync('ava_app.db');
      await this.createTables();
      await this.seedInitialData();
      await this.importCatalogData();
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Users table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        avatar TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Transactions table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('buy', 'sell', 'trade')),
        item_name TEXT NOT NULL,
        amount INTEGER NOT NULL,
        price REAL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      );
    `);

    // Collection items table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS collection_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        image_url TEXT,
        rarity TEXT NOT NULL CHECK (rarity IN ('common', 'rare', 'epic', 'legendary')),
        acquired_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      );
    `);

    // Catalog items table (populated by Python sync script)
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS catalog_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        mongo_id TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        image_url TEXT,
        price REAL,
        category TEXT,
        rarity TEXT,
        set_name TEXT,
        card_number TEXT,
        artist TEXT,
        type TEXT,
        mana_cost TEXT,
        power TEXT,
        toughness TEXT,
        text TEXT,
        flavor_text TEXT,
        raw_data TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create indexes for better search performance
    await this.db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_catalog_name ON catalog_items(name);
    `);
    await this.db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_catalog_category ON catalog_items(category);
    `);
    await this.db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_catalog_set ON catalog_items(set_name);
    `);
    await this.db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_catalog_rarity ON catalog_items(rarity);
    `);
  }

  private async seedInitialData(): Promise<void> {
    if (!this.db) return;

    // Check if user already exists
    const existingUser = await this.db.getFirstAsync('SELECT * FROM users WHERE name = ?', ['Ava']);
    
    if (!existingUser) {
      // Create default user (Ava)
      await this.db.runAsync(
        'INSERT INTO users (name) VALUES (?)',
        ['Ava']
      );

      // Add some sample collection items
      const userId = 1; // Ava's user ID
      const sampleItems = [
        { name: 'Digital Card #001', rarity: 'common', description: 'A basic digital trading card' },
        { name: 'Rare Crystal', rarity: 'rare', description: 'A beautiful rare crystal' },
        { name: 'Epic Sword', rarity: 'epic', description: 'A legendary weapon' },
      ];

      for (const item of sampleItems) {
        await this.db.runAsync(
          'INSERT INTO collection_items (user_id, name, description, rarity) VALUES (?, ?, ?, ?)',
          [userId, item.name, item.description, item.rarity]
        );
      }
    }
  }

  // User methods
  async getUser(id: number): Promise<User | null> {
    if (!this.db) return null;
    return await this.db.getFirstAsync('SELECT * FROM users WHERE id = ?', [id]) as User | null;
  }

  async getUserByName(name: string): Promise<User | null> {
    if (!this.db) return null;
    return await this.db.getFirstAsync('SELECT * FROM users WHERE name = ?', [name]) as User | null;
  }

  // Transaction methods
  async addTransaction(transaction: Omit<Transaction, 'id' | 'created_at'>): Promise<void> {
    if (!this.db) return;
    await this.db.runAsync(
      'INSERT INTO transactions (user_id, type, item_name, amount, price) VALUES (?, ?, ?, ?, ?)',
      [transaction.user_id, transaction.type, transaction.item_name, transaction.amount, transaction.price || null]
    );
  }

  async getTransactions(userId: number): Promise<Transaction[]> {
    if (!this.db) return [];
    return await this.db.getAllAsync('SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC', [userId]) as Transaction[];
  }

  // Collection methods
  async getCollectionItems(userId: number): Promise<CollectionItem[]> {
    if (!this.db) return [];
    return await this.db.getAllAsync('SELECT * FROM collection_items WHERE user_id = ? ORDER BY acquired_at DESC', [userId]) as CollectionItem[];
  }

  async addCollectionItem(item: Omit<CollectionItem, 'id' | 'acquired_at'>): Promise<void> {
    if (!this.db) return;
    await this.db.runAsync(
      'INSERT INTO collection_items (user_id, name, description, image_url, rarity) VALUES (?, ?, ?, ?, ?)',
      [item.user_id, item.name, item.description || null, item.image_url || null, item.rarity]
    );
  }

  async addCatalogItemToCollection(userId: number, catalogItemId: number): Promise<void> {
    if (!this.db) return;

    // Get catalog item details
    const catalogItem = await this.db.getFirstAsync(
      'SELECT * FROM catalog_items WHERE id = ?',
      [catalogItemId]
    ) as CatalogItem;

    if (catalogItem) {
      await this.db.runAsync(
        'INSERT INTO collection_items (user_id, name, description, image_url, rarity) VALUES (?, ?, ?, ?, ?)',
        [userId, catalogItem.name, catalogItem.description || null, catalogItem.image_url || null, catalogItem.rarity || 'common']
      );
    }
  }

  async updateCollectionItem(itemId: number, updates: Partial<CollectionItem>): Promise<void> {
    if (!this.db) return;

    const setClause = [];
    const values = [];

    if (updates.name !== undefined) {
      setClause.push('name = ?');
      values.push(updates.name);
    }
    if (updates.description !== undefined) {
      setClause.push('description = ?');
      values.push(updates.description);
    }
    if (updates.rarity !== undefined) {
      setClause.push('rarity = ?');
      values.push(updates.rarity);
    }

    if (setClause.length > 0) {
      values.push(itemId);
      await this.db.runAsync(
        `UPDATE collection_items SET ${setClause.join(', ')} WHERE id = ?`,
        values
      );
    }
  }

  async deleteCollectionItem(itemId: number): Promise<void> {
    if (!this.db) return;
    await this.db.runAsync('DELETE FROM collection_items WHERE id = ?', [itemId]);
  }

  async isItemInCollection(userId: number, itemName: string): Promise<boolean> {
    if (!this.db) return false;
    const result = await this.db.getFirstAsync(
      'SELECT COUNT(*) as count FROM collection_items WHERE user_id = ? AND name = ?',
      [userId, itemName]
    ) as { count: number };
    return result.count > 0;
  }

  // Catalog search methods (data populated by Python sync script)

  async searchCatalog(query: string, limit: number = 50): Promise<CatalogItem[]> {
    if (!this.db) return [];

    const searchQuery = `%${query.toLowerCase()}%`;
    return await this.db.getAllAsync(`
      SELECT * FROM catalog_items
      WHERE LOWER(name) LIKE ?
         OR LOWER(description) LIKE ?
         OR LOWER(text) LIKE ?
         OR LOWER(artist) LIKE ?
         OR LOWER(set_name) LIKE ?
      ORDER BY
        CASE
          WHEN LOWER(name) LIKE ? THEN 1
          WHEN LOWER(name) LIKE ? THEN 2
          ELSE 3
        END,
        name
      LIMIT ?
    `, [
      searchQuery, searchQuery, searchQuery, searchQuery, searchQuery,
      `${query.toLowerCase()}%`, searchQuery, limit
    ]) as CatalogItem[];
  }

  async getCatalogByCategory(category: string): Promise<CatalogItem[]> {
    if (!this.db) return [];
    return await this.db.getAllAsync('SELECT * FROM catalog_items WHERE category = ? ORDER BY name', [category]) as CatalogItem[];
  }

  async getCatalogBySet(setName: string): Promise<CatalogItem[]> {
    if (!this.db) return [];
    return await this.db.getAllAsync('SELECT * FROM catalog_items WHERE set_name = ? ORDER BY card_number', [setName]) as CatalogItem[];
  }

  async getAllCatalogItems(limit: number = 100): Promise<CatalogItem[]> {
    if (!this.db) return [];
    return await this.db.getAllAsync('SELECT * FROM catalog_items ORDER BY name LIMIT ?', [limit]) as CatalogItem[];
  }

  async getCatalogItemCount(): Promise<number> {
    if (!this.db) return 0;
    const result = await this.db.getFirstAsync('SELECT COUNT(*) as count FROM catalog_items') as { count: number };
    return result.count;
  }

  async getCatalogByRarity(rarity: string): Promise<CatalogItem[]> {
    if (!this.db) return [];
    return await this.db.getAllAsync('SELECT * FROM catalog_items WHERE rarity = ? ORDER BY name', [rarity]) as CatalogItem[];
  }

  async getCatalogSets(): Promise<string[]> {
    if (!this.db) return [];
    const results = await this.db.getAllAsync('SELECT DISTINCT set_name FROM catalog_items WHERE set_name IS NOT NULL ORDER BY set_name') as { set_name: string }[];
    return results.map(r => r.set_name);
  }

  async getCatalogCategories(): Promise<string[]> {
    if (!this.db) return [];
    const results = await this.db.getAllAsync('SELECT DISTINCT category FROM catalog_items WHERE category IS NOT NULL ORDER BY category') as { category: string }[];
    return results.map(r => r.category);
  }

  async getCatalogRarities(): Promise<string[]> {
    if (!this.db) return [];
    const results = await this.db.getAllAsync('SELECT DISTINCT rarity FROM catalog_items WHERE rarity IS NOT NULL ORDER BY rarity') as { rarity: string }[];
    return results.map(r => r.rarity);
  }
}

// Export singleton instance
export const database = new Database();
