"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSetAccessor = void 0;
const assert_1 = require("../assert");
const OrderedCollection_1 = require("./OrderedCollection");
/** @internal */
function createSetAccessor(options) {
    return options.itemType === 9 /* binding.PropertyType.Mixed */
        ? createSetAccessorForMixed(options)
        : createSetAccessorForKnownType(options);
}
exports.createSetAccessor = createSetAccessor;
function createSetAccessorForMixed({ realm, typeHelpers, }) {
    const { fromBinding, toBinding } = typeHelpers;
    return {
        get(set, index) {
            // Core will not return collections within a Set.
            return fromBinding(set.getAny(index));
        },
        // Directly setting by "index" to a Set is a no-op.
        set: () => { },
        insert(set, value) {
            assert_1.assert.inTransaction(realm);
            try {
                set.insertAny(toBinding(value));
            }
            catch (err) {
                // Optimize for the valid cases by not guarding for the unsupported nested collections upfront.
                throw transformError(err);
            }
        },
    };
}
function createSetAccessorForKnownType({ realm, typeHelpers, itemType, }) {
    const { fromBinding, toBinding } = typeHelpers;
    return {
        get: (0, OrderedCollection_1.createDefaultGetter)({ fromBinding, itemType }),
        // Directly setting by "index" to a Set is a no-op.
        set: () => { },
        insert(set, value) {
            assert_1.assert.inTransaction(realm);
            try {
                set.insertAny(toBinding(value));
            }
            catch (err) {
                // Optimize for the valid cases by not guarding for the unsupported nested collections upfront.
                throw transformError(err);
            }
        },
    };
}
function transformError(err) {
    const message = err instanceof Error ? err.message : "";
    if (message?.includes("'Array' to a Mixed") || message?.includes("'List' to a Mixed")) {
        return new Error("Lists within a Set are not supported.");
    }
    if (message?.includes("'Object' to a Mixed") || message?.includes("'Dictionary' to a Mixed")) {
        return new Error("Dictionaries within a Set are not supported.");
    }
    return err;
}
//# sourceMappingURL=Set.js.map