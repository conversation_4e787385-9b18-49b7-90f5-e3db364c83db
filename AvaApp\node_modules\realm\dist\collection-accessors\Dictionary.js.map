{"version": 3, "file": "Dictionary.js", "sourceRoot": "", "sources": ["../../src/collection-accessors/Dictionary.ts"], "names": [], "mappings": ";AAAA,4EAA4E;AAC5E,EAAE;AACF,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,6CAA6C;AAC7C,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,EAAE;AACF,4EAA4E;;;AAE5E,wCAAqC;AACrC,sCAAmC;AACnC,0CAAuC;AAEvC,iCAAoF;AAiBpF,gBAAgB;AAChB,SAAgB,wBAAwB,CAAI,OAA4C;IACtF,OAAO,OAAO,CAAC,QAAQ,uCAA+B;QACpD,CAAC,CAAC,gCAAgC,CAAI,OAAO,CAAC;QAC9C,CAAC,CAAC,oCAAoC,CAAI,OAAO,CAAC,CAAC;AACvD,CAAC;AAJD,4DAIC;AAED,SAAS,gCAAgC,CAAI,EAC3C,KAAK,EACL,WAAW,GACwD;IACnE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC;IAC/C,OAAO;QACL,GAAG,CAAC,UAAU,EAAE,GAAG;YACjB,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACxC,QAAQ,KAAK,EAAE;gBACb,KAAK,iBAAO,CAAC,YAAY,CAAC,CAAC;oBACzB,MAAM,QAAQ,GAAG,IAAA,yBAAkB,EAAI,EAAE,KAAK,EAAE,QAAQ,oCAA4B,EAAE,WAAW,EAAE,CAAC,CAAC;oBACrG,OAAO,IAAI,mBAAQ,CAAC,IAAI,CAAI,KAAK,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,QAAQ,EAAE,WAAW,CAAM,CAAC;iBACzF;gBACD,KAAK,iBAAO,CAAC,kBAAkB,CAAC,CAAC;oBAC/B,MAAM,QAAQ,GAAG,wBAAwB,CAAI,EAAE,KAAK,EAAE,QAAQ,oCAA4B,EAAE,WAAW,EAAE,CAAC,CAAC;oBAC3G,OAAO,IAAI,mBAAQ,CAAC,UAAU,CAAI,KAAK,EAAE,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,QAAQ,EAAE,WAAW,CAAM,CAAC;iBACrG;gBACD;oBACE,OAAO,WAAW,CAAC,KAAK,CAAM,CAAC;aAClC;QACH,CAAC;QACD,GAAG,CAAC,UAAU,EAAE,GAAG,EAAE,KAAK;YACxB,eAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAE5B,IAAI,IAAA,sBAAe,EAAC,KAAK,CAAC,EAAE;gBAC1B,UAAU,CAAC,gBAAgB,CAAC,GAAG,uCAA8B,CAAC;gBAC9D,IAAA,4BAAqB,EAAC,KAAK,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC;aAClE;iBAAM,IAAI,qBAAqB,CAAC,KAAK,CAAC,EAAE;gBACvC,UAAU,CAAC,gBAAgB,CAAC,GAAG,6CAAoC,CAAC;gBACpE,2BAA2B,CAAC,KAAK,EAAE,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC;aAC9E;iBAAM;gBACL,UAAU,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;aAC7C;QACH,CAAC;KACF,CAAC;AACJ,CAAC;AAED,SAAS,oCAAoC,CAAI,EAC/C,KAAK,EACL,WAAW,EACX,UAAU,GAC4C;IACtD,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,WAAW,CAAC;IAC/C,OAAO;QACL,GAAG,CAAC,UAAU,EAAE,GAAG;YACjB,OAAO,WAAW,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;QAChD,CAAC;QACD,GAAG,CAAC,UAAU,EAAE,GAAG,EAAE,KAAK;YACxB,eAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAE5B,IAAI,UAAU,EAAE;gBACd,SAAS,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;aAC/E;iBAAM;gBACL,UAAU,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;aAC7C;QACH,CAAC;KACF,CAAC;AACJ,CAAC;AAED,gBAAgB;AAChB,SAAgB,2BAA2B,CACzC,UAAgD,EAChD,QAA4B,EAC5B,SAAmC;IAEnC,4GAA4G;IAC5G,QAAQ,CAAC,SAAS,EAAE,CAAC;IAErB,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE;QAC5B,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,IAAA,sBAAe,EAAC,KAAK,CAAC,EAAE;YAC1B,QAAQ,CAAC,gBAAgB,CAAC,GAAG,uCAA8B,CAAC;YAC5D,IAAA,4BAAqB,EAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC;SAChE;aAAM,IAAI,qBAAqB,CAAC,KAAK,CAAC,EAAE;YACvC,QAAQ,CAAC,gBAAgB,CAAC,GAAG,6CAAoC,CAAC;YAClE,2BAA2B,CAAC,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC;SAC5E;aAAM;YACL,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;SAC3C;KACF;AACH,CAAC;AApBD,kEAoBC;AAED,gBAAgB;AAChB,SAAgB,qBAAqB,CAAC,KAAc;IAClD,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,mBAAQ,CAAC,UAAU,CAAC;AAC/D,CAAC;AAFD,sDAEC;AAED,gBAAgB;AAChB,SAAgB,MAAM,CAAC,KAAc;IACnC,OAAO,CACL,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,KAAK,IAAI;QACd,uDAAuD;QACvD,oDAAoD;QACpD,CAAC,KAAK,CAAC,WAAW,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAChE,CAAC;AACJ,CAAC;AARD,wBAQC"}