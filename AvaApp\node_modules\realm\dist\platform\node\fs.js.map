{"version": 3, "file": "fs.js", "sourceRoot": "", "sources": ["../../../src/platform/node/fs.ts"], "names": [], "mappings": ";AAAA,4EAA4E;AAC5E,EAAE;AACF,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,6CAA6C;AAC7C,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,EAAE;AACF,4EAA4E;;AAE5E,qCAAiF;AACjF,yCAAsD;AAEtD,gDAAwC;AACxC,uCAA0C;AAC1C,wCAAqC;AAErC,MAAM,KAAK,GAAG,IAAA,mBAAW,EAAC,IAAI,CAAC,CAAC;AAEhC,IAAA,oBAAM,EAAC;IACL,cAAc,CAAC,IAAI;QACjB,OAAO,IAAA,sBAAU,EAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IACD,SAAS,CAAC,GAAG,QAAQ;QACnB,OAAO,IAAA,gBAAI,EAAC,GAAG,QAAQ,CAAC,CAAC;IAC3B,CAAC;IACD,UAAU,CAAC,IAAI;QACb,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAC1B,IAAI,IAAA,oBAAU,EAAC,IAAI,CAAC,EAAE;YACpB,IAAA,oBAAU,EAAC,IAAI,CAAC,CAAC;SAClB;IACH,CAAC;IACD,eAAe,CAAC,IAAI;QAClB,KAAK,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAC/B,IAAA,gBAAM,EAAC,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;IACjD,CAAC;IACD,sBAAsB,CAAC,IAAI;QACzB,MAAM,UAAU,GAAG,IAAA,mBAAO,EAAC,IAAI,CAAC,CAAC;QACjC,IAAA,mBAAS,EAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC7C,CAAC;IACD,uBAAuB,CAAC,IAAI;QAC1B,KAAK,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC;QACvC,OAAO,iBAAO,CAAC,iBAAiB,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;IACtE,CAAC;IACD,uBAAuB;QACrB,OAAO,iBAAO,CAAC,iBAAiB,CAAC,yBAAyB,EAAE,CAAC;IAC/D,CAAC;IACD,MAAM,CAAC,IAAI;QACT,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACtB,OAAO,IAAA,oBAAU,EAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IACD,qBAAqB;QACnB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IAClE,CAAC;IACD;;;;MAIE;IACF,6BAA6B,CAAC,IAAY;QACxC,KAAK,CAAC,+BAA+B,EAAE,IAAI,CAAC,CAAC;QAC7C,KAAK,MAAM,MAAM,IAAI,IAAA,qBAAW,EAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,EAAE;YACjF,MAAM,UAAU,GAAG,IAAA,gBAAI,EAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;gBACrE,IAAA,gBAAM,EAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;aACtD;iBAAM,IACL,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAC9B,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;gBACnC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;gBACnC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC;gBACzC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAClC;gBACA,IAAA,gBAAM,EAAC,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;aACrC;SACF;IACH,CAAC;CACF,CAAC,CAAC"}