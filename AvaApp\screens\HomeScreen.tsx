import React, { useEffect, useState } from 'react';
import { View, StyleSheet, SafeAreaView, Alert, ScrollView } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { StatusBar } from 'expo-status-bar';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Colors, Gradients } from '../constants/Colors';
import { GreetingBubble } from '../components/GreetingBubble';
import { CharacterDisplay } from '../components/CharacterDisplay';
import { ActionButton } from '../components/ActionButton';
import { database, User } from '../database/database';
import { RootStackParamList } from '../types/navigation';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;

export const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      await database.init();
      const avaUser = await database.getUserByName('Ava');
      setUser(avaUser);
    } catch (error) {
      console.error('Failed to initialize app:', error);
      Alert.alert('Error', 'Failed to initialize the app. Please restart.');
    } finally {
      setLoading(false);
    }
  };

  const handleBuy = () => {
    navigation.navigate('Buy');
  };

  const handleSell = () => {
    navigation.navigate('Sell');
  };

  const handleCollection = () => {
    navigation.navigate('Collection');
  };

  const handleTrade = () => {
    navigation.navigate('Trade');
  };

  if (loading) {
    return (
      <LinearGradient colors={Gradients.background} style={styles.container}>
        <StatusBar style="light" />
      </LinearGradient>
    );
  }

  return (
    <LinearGradient colors={Gradients.background} style={styles.container}>
      <StatusBar style="light" />
      <SafeAreaView style={styles.safeArea}>
        <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
          <GreetingBubble 
            name={user?.name || 'Ava'} 
            message="What would you like to do today?" 
          />
          
          <CharacterDisplay name={user?.name || 'Ava'} />
          
          <View style={styles.actionsContainer}>
            <View style={styles.actionRow}>
              <ActionButton
                title="Buy"
                icon="cart-outline"
                onPress={handleBuy}
              />
              <ActionButton
                title="Sell"
                icon="cash-outline"
                onPress={handleSell}
              />
            </View>
            <View style={styles.actionRow}>
              <ActionButton
                title="Collection"
                icon="library-outline"
                onPress={handleCollection}
              />
              <ActionButton
                title="Trade"
                icon="swap-horizontal-outline"
                onPress={handleTrade}
              />
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 40,
  },
  actionsContainer: {
    paddingHorizontal: 20,
    marginTop: 20,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
});
