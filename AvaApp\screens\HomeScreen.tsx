import React, { useEffect, useState } from 'react';
import { View, StyleSheet, SafeAreaView, Alert, ScrollView } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { StatusBar } from 'expo-status-bar';
import { Colors, Gradients } from '../constants/Colors';
import { GreetingBubble } from '../components/GreetingBubble';
import { CharacterDisplay } from '../components/CharacterDisplay';
import { ActionButton } from '../components/ActionButton';
import { database, User } from '../database/database';

export const HomeScreen: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      await database.init();
      const avaUser = await database.getUserByName('Ava');
      setUser(avaUser);
    } catch (error) {
      console.error('Failed to initialize app:', error);
      Alert.alert('Error', 'Failed to initialize the app. Please restart.');
    } finally {
      setLoading(false);
    }
  };

  const handleBuy = async () => {
    if (!user) return;
    
    try {
      await database.addTransaction({
        user_id: user.id,
        type: 'buy',
        item_name: 'Sample Item',
        amount: 1,
        price: 10.99,
      });
      Alert.alert('Success', 'Item purchased successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to process purchase.');
    }
  };

  const handleSell = async () => {
    if (!user) return;
    
    try {
      await database.addTransaction({
        user_id: user.id,
        type: 'sell',
        item_name: 'Sample Item',
        amount: 1,
        price: 8.99,
      });
      Alert.alert('Success', 'Item sold successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to process sale.');
    }
  };

  const handleCollection = async () => {
    if (!user) return;
    
    try {
      const items = await database.getCollectionItems(user.id);
      Alert.alert(
        'Collection', 
        `You have ${items.length} items in your collection:\n${items.map(item => `• ${item.name}`).join('\n')}`
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to load collection.');
    }
  };

  const handleTrade = async () => {
    if (!user) return;
    
    try {
      await database.addTransaction({
        user_id: user.id,
        type: 'trade',
        item_name: 'Traded Item',
        amount: 1,
      });
      Alert.alert('Success', 'Trade completed successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to process trade.');
    }
  };

  if (loading) {
    return (
      <LinearGradient colors={Gradients.background} style={styles.container}>
        <StatusBar style="light" />
      </LinearGradient>
    );
  }

  return (
    <LinearGradient colors={Gradients.background} style={styles.container}>
      <StatusBar style="light" />
      <SafeAreaView style={styles.safeArea}>
        <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
          <GreetingBubble 
            name={user?.name || 'Ava'} 
            message="What would you like to do today?" 
          />
          
          <CharacterDisplay name={user?.name || 'Ava'} />
          
          <View style={styles.actionsContainer}>
            <View style={styles.actionRow}>
              <ActionButton
                title="Buy"
                icon="cart-outline"
                onPress={handleBuy}
              />
              <ActionButton
                title="Sell"
                icon="cash-outline"
                onPress={handleSell}
              />
            </View>
            <View style={styles.actionRow}>
              <ActionButton
                title="Collection"
                icon="library-outline"
                onPress={handleCollection}
              />
              <ActionButton
                title="Trade"
                icon="swap-horizontal-outline"
                onPress={handleTrade}
              />
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 40,
  },
  actionsContainer: {
    paddingHorizontal: 20,
    marginTop: 20,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
});
