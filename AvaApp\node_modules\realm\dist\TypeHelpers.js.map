{"version": 3, "file": "TypeHelpers.js", "sourceRoot": "", "sources": ["../src/TypeHelpers.ts"], "names": [], "mappings": ";AAAA,4EAA4E;AAC5E,EAAE;AACF,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,6CAA6C;AAC7C,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,EAAE;AACF,4EAA4E;;;AAG5E,qCAAkC;AAElC,4CAA0D;AAC1D,8CAA4D;AAC5D,kDAAgE;AAChE,8CAA4D;AAC5D,8CAA4D;AAC5D,gDAA8D;AAC9D,sDAAoE;AACpE,gDAA8D;AAC9D,kDAAgE;AAChE,kDAAgE;AAChE,kEAAgF;AAChF,oDAAkE;AAClE,8CAA4D;AAC5D,gDAA8D;AAQ9D,SAAS,4BAA4B;IACnC,OAAO;QACL,WAAW;YACT,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACvC,CAAC;QACD,SAAS;YACP,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACvC,CAAC;KACF,CAAC;AACJ,CAAC;AAQD,MAAM,aAAa,GAAuE;IACxF,kCAA0B,EAAE,0BAAoB;IAChD,mCAA2B,EAAE,4BAAqB;IAClD,qCAA6B,EAAE,gCAAuB;IACtD,mCAA2B,EAAE,4BAAqB;IAClD,mCAA2B,EAAE,4BAAqB;IAClD,oCAA4B,EAAE,8BAAsB;IACpD,qCAA6B,EAAE,gCAAuB;IACtD,qCAA6B,EAAE,gCAAuB;IACtD,6CAAqC,EAAE,gDAA+B;IACtE,oCAA4B,EAAE,8BAAsB;IACpD,wCAA+B,EAAE,oCAAyB;IAC1D,uCAA8B,EAAE,kCAAwB;IACxD,oCAA2B,EAAE,4BAAqB;IAClD,sCAA4B,EAAE,8BAAsB;IACpD,oBAAoB;IACpB,oCAA0B,EAAE,4BAA4B;IACxD,2CAAiC,EAAE,4BAA4B;CAChE,CAAC;AAEF,gBAAgB;AAChB,SAAgB,UAAU,CAAC,IAA0B;IACnD,OAAO,IAAI,GAAG,qCAA2B,CAAC;AAC5C,CAAC;AAFD,gCAEC;AAED,gBAAgB;AAChB,SAAgB,cAAc,CAAC,IAAyB,EAAE,OAAoB;IAC5E,MAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;IACpC,IAAA,eAAM,EAAC,OAAO,EAAE,mBAAmB,IAAI,EAAE,CAAC,CAAC;IAC3C,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC;AAC1B,CAAC;AAJD,wCAIC"}