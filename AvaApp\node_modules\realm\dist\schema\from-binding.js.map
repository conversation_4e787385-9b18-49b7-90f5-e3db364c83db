{"version": 3, "file": "from-binding.js", "sourceRoot": "", "sources": ["../../src/schema/from-binding.ts"], "names": [], "mappings": ";AAAA,4EAA4E;AAC5E,EAAE;AACF,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,6CAA6C;AAC7C,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,EAAE;AACF,4EAA4E;;;AAG5E,sCAAmC;AASnC,MAAM,aAAa,GAAyD;IAC1E,iCAAyB,EAAE,KAAK;IAChC,kCAA0B,EAAE,MAAM;IAClC,oCAA4B,EAAE,QAAQ;IACtC,kCAA0B,EAAE,MAAM;IAClC,kCAA0B,EAAE,MAAM;IAClC,mCAA2B,EAAE,OAAO;IACpC,oCAA4B,EAAE,QAAQ;IACtC,mCAA2B,EAAE,OAAO;IACpC,uCAA8B,EAAE,UAAU;IAC1C,sCAA6B,EAAE,YAAY;IAC3C,mCAA0B,EAAE,MAAM;IAClC,qCAA2B,EAAE,MAAM;IACnC,mCAAyB,EAAE,KAAK;IAChC,0CAAgC,EAAE,YAAY;IAC9C,4CAAoC,EAAE,gBAAgB;IACtD,oCAA4B,EAAE,QAAQ;IACtC,uBAAuB;IACvB,uCAA8B,EAAE,IAAI;IACpC,EAAE;IACF,0CAAgC,EAAE,IAAI;IACtC,qCAA2B,EAAE,IAAI;CAClC,CAAC;AAEF;;;GAGG;AACH,SAAgB,WAAW,CAAC,IAAyB,EAAE,UAA8B;IACnF,MAAM,QAAQ,GAAG,IAAI,GAAG,oCAA0B,CAAC;IACnD,IAAI,IAAI,sCAA4B,EAAE;QACpC,IAAI,QAAQ,uCAA+B,EAAE;YAC3C,OAAO,QAAQ,UAAU,GAAG,CAAC;SAC9B;aAAM;YACL,OAAO,QAAQ,WAAW,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,CAAC;SACrD;KACF;SAAM,IAAI,IAAI,oCAA0B,EAAE;QACzC,OAAO,OAAO,WAAW,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,CAAC;KACpD;SAAM,IAAI,IAAI,2CAAiC,EAAE;QAChD,OAAO,cAAc,WAAW,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,CAAC;KAC3D;SAAM,IAAI,QAAQ,uCAA+B,IAAI,UAAU,EAAE;QAChE,eAAM,CAAC,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QACxC,OAAO,IAAI,UAAU,GAAG,CAAC;KAC1B;SAAM;QACL,MAAM,MAAM,GAAG,aAAa,CAAC,QAA+B,CAAC,CAAC;QAC9D,IAAA,eAAM,EAAC,MAAM,EAAE,mBAAmB,IAAI,EAAE,CAAC,CAAC;QAC1C,OAAO,MAAM,CAAC;KACf;AACH,CAAC;AApBD,kCAoBC;AAED,MAAM,gBAAgB,GAAG,kHAAoF,CAAC;AAE9G;;;GAGG;AACH,SAAgB,uBAAuB,CAAC,EACtC,IAAI,EACJ,kBAAkB,EAClB,mBAAmB,EACnB,UAAU,EACV,SAAS,GACW;IACpB,MAAM,UAAU,GAAG,CAAC,GAAG,kBAAkB,EAAE,GAAG,mBAAmB,CAAC,CAAC;IACnE,MAAM,MAAM,GAA0B;QACpC,IAAI,EAAE,SAAS;QACf,IAAI;QACJ,UAAU,EAAE,MAAM,CAAC,WAAW,CAC5B,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,IAAI,EAAE,yBAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAC1G;QACD,QAAQ,EAAE,SAAS,+BAAuB;QAC1C,UAAU,EAAE,SAAS,yCAAiC;KACvD,CAAC;IACF,mEAAmE;IACnE,IAAI,UAAU,EAAE;QACd,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;KAChC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAtBD,0DAsBC;AAED;;;GAGG;AACH,SAAgB,yBAAyB,CAAC,cAA+B;IACvE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,iBAAiB,EAAE,UAAU,EAAE,GAAG,cAAc,CAAC;IAC1E,MAAM,MAAM,GAA4B;QACtC,IAAI;QACJ,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;QACpD,KAAK,EAAE,IAAI;QACX,GAAG,2BAA2B,CAAC,cAAc,CAAC;KAC/C,CAAC;IACF,IAAI,UAAU,EAAE;QACd,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC;KAC1B;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAZD,8DAYC;AAED,gBAAgB;AAChB,SAAS,2BAA2B,CAClC,cAA+B;IAE/B,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,sBAAsB,EAAE,GAAG,cAAc,CAAC;IACpE,MAAM,QAAQ,GAAG,IAAI,GAAG,yCAA+B,CAAC;IAExD,IAAI,IAAI,wCAA+B,EAAE;QACvC,MAAM,IAAI,GAAG,2BAA2B,CAAC,EAAE,GAAG,cAAc,EAAE,IAAI,EAAE,IAAI,GAAG,sCAA6B,EAAE,CAAC,CAAC;QAC5G,OAAO,EAAE,GAAG,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;KACpC;IAED,IAAI,QAAQ,+CAAuC,EAAE;QACnD,IAAA,eAAM,EAAC,IAAI,sCAA4B,CAAC,CAAC;QACzC,eAAM,CAAC,MAAM,CAAC,sBAAsB,EAAE,wBAAwB,CAAC,CAAC;QAChE,OAAO;YACL,IAAI,EAAE,gBAAgB;YACtB,QAAQ,EAAE,KAAK;YACf,UAAU;YACV,QAAQ,EAAE,sBAAsB;SACjC,CAAC;KACH;IAED,KAAK,MAAM,cAAc,IAAI,gBAAgB,EAAE;QAC7C,IAAI,IAAI,GAAG,cAAc,EAAE;YACzB,MAAM,IAAI,GAAG,2BAA2B,CAAC,EAAE,GAAG,cAAc,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;YAChF,OAAO;gBACL,IAAI,EAAE,aAAa,CAAC,cAAc,CAAqB;gBACvD,UAAU,EAAE,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI;gBAChE,QAAQ,EAAE,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ;aACzD,CAAC;SACH;KACF;IAED,IAAI,IAAI,uCAA+B,EAAE;QACvC,IAAI,CAAC,UAAU,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;SAClF;QACD,4CAA4C;QAC5C,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,sBAAsB;KAC9E;SAAM,IAAI,IAAI,+CAAuC,EAAE;QACtD,IAAI,CAAC,UAAU,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;SAClF;QACD,OAAO,EAAE,IAAI,EAAE,gBAAgB,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;KAChE;IAED,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;IACvC,IAAI,UAAU,EAAE;QACd,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;KAC9C;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,oBAAoB,IAAI,GAAG,CAAC,CAAC;KAC9C;AACH,CAAC;AAED,gBAAgB;AAChB,SAAgB,sBAAsB,CAAC,MAAuC;IAC5E,OAAO,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;AAC7C,CAAC;AAFD,wDAEC"}