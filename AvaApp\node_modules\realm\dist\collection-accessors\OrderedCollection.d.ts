import { binding } from "../binding";
import type { OrderedCollectionInternal } from "../OrderedCollection";
import type { TypeHelpers } from "../TypeHelpers";
type Getter<CollectionType, T> = (collection: CollectionType, index: number) => T;
type GetterFactoryOptions<T> = {
    fromBinding: TypeHelpers<T>["fromBinding"];
    itemType: binding.PropertyType;
};
/** @internal */
export declare function createDefaultGetter<CollectionType extends OrderedCollectionInternal, T>({ fromBinding, itemType, }: GetterFactoryOptions<T>): Getter<CollectionType, T>;
export {};
