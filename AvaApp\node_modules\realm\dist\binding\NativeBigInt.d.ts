import type { binding } from "./wrapper.generated";
export declare class NativeBigInt {
    static add(a: binding.Int64, b: binding.Int64): binding.Int64;
    static equals(a: binding.Int64, b: binding.Int64 | number | string): boolean;
    static isInt(a: unknown): a is binding.Int64;
    static numToInt(a: number): binding.Int64;
    static strToInt(a: string): binding.Int64;
    static intToNum(a: binding.Int64): number;
}
