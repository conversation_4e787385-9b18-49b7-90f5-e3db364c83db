"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDefaultGetter = void 0;
/** @internal */
function createDefaultGetter({ fromBinding, itemType, }) {
    const isObjectItem = itemType === 7 /* binding.PropertyType.Object */ || itemType === 8 /* binding.PropertyType.LinkingObjects */;
    return isObjectItem ? (...args) => getObject(fromBinding, ...args) : (...args) => getKnownType(fromBinding, ...args);
}
exports.createDefaultGetter = createDefaultGetter;
function getObject(fromBinding, collection, index) {
    return fromBinding(collection.getObj(index));
}
function getKnownType(fromBinding, collection, index) {
    return fromBinding(collection.getAny(index));
}
//# sourceMappingURL=OrderedCollection.js.map