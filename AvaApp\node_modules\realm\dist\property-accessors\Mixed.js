"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMixedPropertyAccessor = void 0;
const binding_1 = require("../binding");
const assert_1 = require("../assert");
const Dictionary_1 = require("../Dictionary");
const Dictionary_2 = require("../collection-accessors/Dictionary");
const List_1 = require("../List");
const List_2 = require("../collection-accessors/List");
const default_1 = require("./default");
/** @internal */
function createMixedPropertyAccessor(options) {
    const { realm, columnKey, typeHelpers } = options;
    const { fromBinding, toBinding } = typeHelpers;
    const listAccessor = (0, List_2.createListAccessor)({ realm, typeHelpers, itemType: 9 /* binding.PropertyType.Mixed */ });
    const dictionaryAccessor = (0, Dictionary_2.createDictionaryAccessor)({ realm, typeHelpers, itemType: 9 /* binding.PropertyType.Mixed */ });
    const { set: defaultSet } = (0, default_1.createDefaultPropertyAccessor)(options);
    return {
        get(obj) {
            try {
                const value = obj.getAny(columnKey);
                switch (value) {
                    case binding_1.binding.ListSentinel: {
                        const internal = binding_1.binding.List.make(realm.internal, obj, columnKey);
                        return new List_1.List(realm, internal, listAccessor, typeHelpers);
                    }
                    case binding_1.binding.DictionarySentinel: {
                        const internal = binding_1.binding.Dictionary.make(realm.internal, obj, columnKey);
                        return new Dictionary_1.Dictionary(realm, internal, dictionaryAccessor, typeHelpers);
                    }
                    default:
                        return fromBinding(value);
                }
            }
            catch (err) {
                assert_1.assert.isValid(obj);
                throw err;
            }
        },
        set(obj, value) {
            assert_1.assert.inTransaction(realm);
            if ((0, List_2.isJsOrRealmList)(value)) {
                obj.setCollection(columnKey, 19 /* binding.CollectionType.List */);
                const internal = binding_1.binding.List.make(realm.internal, obj, columnKey);
                (0, List_2.insertIntoListOfMixed)(value, internal, toBinding);
            }
            else if ((0, Dictionary_2.isJsOrRealmDictionary)(value)) {
                obj.setCollection(columnKey, 21 /* binding.CollectionType.Dictionary */);
                const internal = binding_1.binding.Dictionary.make(realm.internal, obj, columnKey);
                (0, Dictionary_2.insertIntoDictionaryOfMixed)(value, internal, toBinding);
            }
            else {
                defaultSet(obj, value);
            }
        },
    };
}
exports.createMixedPropertyAccessor = createMixedPropertyAccessor;
//# sourceMappingURL=Mixed.js.map