import { binding } from "./binding";
import type { CanonicalObjectSchema } from "./schema";
import type { HelperOptions, PropertyHelpers } from "./property-accessors/types";
/** @internal */
export declare class PropertyMap {
    private objectSchemaName;
    private initialized;
    private mapping;
    /**
     * Note: Cannot key by the binding.ColKey directly, as this is `Long` on JSC (which does not pass equality checks like `bigint` does)
     */
    private nameByColumnKeyString;
    private _names;
    initialize(objectSchema: binding.ObjectSchema, canonicalObjectSchema: CanonicalObjectSchema, defaults: Record<string, unknown>, options: HelperOptions): void;
    get: (property: string) => PropertyHelpers;
    getName: <T>(columnKey: binding.ColKey) => keyof T;
    get names(): string[];
}
