{"version": 3, "file": "PromiseHandle.js", "sourceRoot": "", "sources": ["../src/PromiseHandle.ts"], "names": [], "mappings": ";AAAA,4EAA4E;AAC5E,EAAE;AACF,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,6CAA6C;AAC7C,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,EAAE;AACF,4EAA4E;;;AAE5E,qCAAkC;AAKlC;;GAEG;AACH,MAAa,aAAa;IACxB,OAAO,CAAkB;IACzB,MAAM,CAAc;IACpB,OAAO,CAAa;IAEpB;QACE,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAI,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;YAC3C,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,EAAE,EAAE;gBACrB,IAAI,CAAC,GAAG,CAAC,CAAC;YACZ,CAAC,CAAC;YACF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC,CAAC,CAAC;QACH,IAAA,eAAM,EAAC,IAAI,CAAC,OAAO,EAAE,sDAAsD,CAAC,CAAC;QAC7E,IAAA,eAAM,EAAC,IAAI,CAAC,MAAM,EAAE,sDAAsD,CAAC,CAAC;IAC9E,CAAC;CACF;AAfD,sCAeC"}